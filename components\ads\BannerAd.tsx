import React, { useEffect, useState } from 'react';
import { Dimensions, StyleSheet, Text, View } from 'react-native';
import { BannerAd, BannerAdSize } from 'react-native-google-mobile-ads';
import { ADMOB_CONFIG, getAdUnitIdForEnvironment } from '../../services/admobService';

interface BannerAdComponentProps {
  adLocation: 'home' | 'explore' | 'female' | 'settings';
  size?: BannerAdSize;
  style?: any;
  onAdLoaded?: () => void;
  onAdFailedToLoad?: (error: any) => void;
}

const BannerAdComponent: React.FC<BannerAdComponentProps> = ({
  adLocation,
  size = BannerAdSize.BANNER,
  style,
  onAdLoaded,
  onAdFailedToLoad,
}) => {
  const [adUnitId, setAdUnitId] = useState<string>('');
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Get the appropriate ad unit ID for the location
    const adConfig = ADMOB_CONFIG.BANNER_ADS[adLocation];
    const unitId = getAdUnitIdForEnvironment(adConfig, 'banner');
    setAdUnitId(unitId);
  }, [adLocation]);

  const handleAdLoaded = () => {
    setIsLoaded(true);
    setError(null);
    onAdLoaded?.();
  };

  const handleAdFailedToLoad = (error: any) => {
    setIsLoaded(false);
    setError(error.message || 'Failed to load ad');
    onAdFailedToLoad?.(error);
    console.warn(`Banner ad failed to load for ${adLocation}:`, error);
  };

  const handleAdOpened = () => {
    console.log(`Banner ad opened for ${adLocation}`);
  };

  const handleAdClosed = () => {
    console.log(`Banner ad closed for ${adLocation}`);
  };

  if (!adUnitId) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <BannerAd
        unitId={adUnitId}
        size={size}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false,
          networkExtras: {
            // Add any network-specific extras here
          },
        }}
        onAdLoaded={handleAdLoaded}
        onAdFailedToLoad={handleAdFailedToLoad}
        onAdOpened={handleAdOpened}
        onAdClosed={handleAdClosed}
      />
      {/* Optional: Add loading indicator or error message */}
      {error && __DEV__ && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Ad Error: {error}</Text>
        </View>
      )}
    </View>
  );
};

// Smart Banner component that adapts to screen size
export const SmartBannerAd: React.FC<Omit<BannerAdComponentProps, 'size'>> = (props) => {
  const screenWidth = Dimensions.get('window').width;
  
  // Choose appropriate banner size based on screen width
  let bannerSize = BannerAdSize.BANNER;
  
  if (screenWidth >= 728) {
    bannerSize = BannerAdSize.LEADERBOARD;
  } else if (screenWidth >= 468) {
    bannerSize = BannerAdSize.MEDIUM_RECTANGLE;
  } else if (screenWidth >= 320) {
    bannerSize = BannerAdSize.BANNER;
  }

  return <BannerAdComponent {...props} size={bannerSize} />;
};

// Adaptive Banner component (recommended for most use cases)
export const AdaptiveBannerAd: React.FC<Omit<BannerAdComponentProps, 'size'>> = (props) => {
  return (
    <BannerAdComponent 
      {...props} 
      size={BannerAdSize.ANCHORED_ADAPTIVE_BANNER}
    />
  );
};

// Large Banner component for prominent placements
export const LargeBannerAd: React.FC<Omit<BannerAdComponentProps, 'size'>> = (props) => {
  return (
    <BannerAdComponent 
      {...props} 
      size={BannerAdSize.LARGE_BANNER}
    />
  );
};

// Medium Rectangle Banner for in-content placements
export const MediumRectangleBannerAd: React.FC<Omit<BannerAdComponentProps, 'size'>> = (props) => {
  return (
    <BannerAdComponent 
      {...props} 
      size={BannerAdSize.MEDIUM_RECTANGLE}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    marginVertical: 10,
  },
  errorContainer: {
    padding: 8,
    backgroundColor: '#ffebee',
    borderRadius: 4,
    marginTop: 4,
  },
  errorText: {
    color: '#c62828',
    fontSize: 12,
    textAlign: 'center',
  },
});

export default BannerAdComponent;
