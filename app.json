{"expo": {"name": "my-app", "slug": "aimodel", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.utkarsh500500.aimodel"}, "assetBundlePatterns": ["assets/models/*"], "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "react-native-google-mobile-ads"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "e23bf357-2da7-404a-b640-7396f908352d"}}, "owner": "utkarsh500500"}}