{"expo": {"name": "my-app", "slug": "aimodel", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logo.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": false, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/logo.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.utkarsh500500.aimodel"}, "assetBundlePatterns": ["assets/models/*", "assets/images/*"], "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/logo.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/logo.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "d4dd58eb-80e5-4a43-ac55-c23bdb8a017a"}}, "owner": "utkarsh500500"}}