{"expo": {"name": "Skin2 Style", "slug": "aimodel", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logo.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": false, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/logo.png", "backgroundColor": "transparent", "monochromeImage": "./assets/images/logo.png"}, "edgeToEdgeEnabled": true, "package": "com.utkarsh500500.aimodel"}, "assetBundlePatterns": ["assets/models/*", "assets/images/*"], "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/logo.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/logo.png", "imageWidth": 280, "resizeMode": "contain", "backgroundColor": "#000000"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "d4dd58eb-80e5-4a43-ac55-c23bdb8a017a"}}, "owner": "utkarsh500500"}}