{"expo": {"name": "my-app", "slug": "aimodel", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.utkarsh500500.aimodel"}, "assetBundlePatterns": ["assets/models/*"], "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["react-native-google-mobile-ads", {"androidAppId": "ca-app-pub-4977543488988533~1311624736", "iosAppId": "ca-app-pub-3940256099942544~1458002511"}], "react-native-google-mobile-ads"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "55833e3e-da1b-4eee-9910-1c0adbd68a43"}}, "owner": "utkarsh500500"}}