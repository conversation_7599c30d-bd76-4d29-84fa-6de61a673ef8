import { useState, useEffect, useCallback } from 'react';
import { RewardedAd, RewardedAdEventType, RewardedAdReward } from 'react-native-google-mobile-ads';
import { 
  getAdUnitIdForEnvironment, 
  ADMOB_CONFIG, 
  canShowRewarded, 
  markRewardedShown 
} from '../services/admobService';

interface UseRewardedAdProps {
  adLocation: 'premiumFeatures' | 'extraColors';
  autoLoad?: boolean;
  onRewardEarned?: (reward: RewardedAdReward) => void;
}

interface UseRewardedAdReturn {
  isLoaded: boolean;
  isLoading: boolean;
  error: string | null;
  showAd: () => Promise<boolean>;
  loadAd: () => void;
  canShow: boolean;
  lastReward: RewardedAdReward | null;
}

export const useRewardedAd = ({ 
  adLocation, 
  autoLoad = true,
  onRewardEarned 
}: UseRewardedAdProps): UseRewardedAdReturn => {
  const [rewardedAd, setRewardedAd] = useState<RewardedAd | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastReward, setLastReward] = useState<RewardedAdReward | null>(null);

  // Initialize the rewarded ad
  useEffect(() => {
    const adConfig = ADMOB_CONFIG.REWARDED_ADS[adLocation];
    const adUnitId = getAdUnitIdForEnvironment(adConfig, 'rewarded');
    
    if (adUnitId) {
      const rewarded = RewardedAd.createForAdRequest(adUnitId, {
        requestNonPersonalizedAdsOnly: false,
        keywords: ['fashion', 'style', 'colors', 'clothing', 'premium'],
      });

      setRewardedAd(rewarded);

      // Set up event listeners
      const unsubscribeLoaded = rewarded.addAdEventListener(
        RewardedAdEventType.LOADED,
        () => {
          setIsLoaded(true);
          setIsLoading(false);
          setError(null);
          console.log(`Rewarded ad loaded for ${adLocation}`);
        }
      );

      const unsubscribeError = rewarded.addAdEventListener(
        RewardedAdEventType.ERROR,
        (error) => {
          setIsLoaded(false);
          setIsLoading(false);
          setError(error.message || 'Failed to load rewarded ad');
          console.warn(`Rewarded ad error for ${adLocation}:`, error);
        }
      );

      const unsubscribeOpened = rewarded.addAdEventListener(
        RewardedAdEventType.OPENED,
        () => {
          console.log(`Rewarded ad opened for ${adLocation}`);
        }
      );

      const unsubscribeClosed = rewarded.addAdEventListener(
        RewardedAdEventType.CLOSED,
        () => {
          console.log(`Rewarded ad closed for ${adLocation}`);
          markRewardedShown();
          setIsLoaded(false);
          // Auto-reload the ad for next time
          if (autoLoad) {
            loadAd();
          }
        }
      );

      const unsubscribeEarnedReward = rewarded.addAdEventListener(
        RewardedAdEventType.EARNED_REWARD,
        (reward) => {
          console.log(`Reward earned for ${adLocation}:`, reward);
          setLastReward(reward);
          onRewardEarned?.(reward);
        }
      );

      // Auto-load if enabled
      if (autoLoad) {
        loadAd();
      }

      // Cleanup function
      return () => {
        unsubscribeLoaded();
        unsubscribeError();
        unsubscribeOpened();
        unsubscribeClosed();
        unsubscribeEarnedReward();
      };
    }
  }, [adLocation, autoLoad, onRewardEarned]);

  const loadAd = useCallback(() => {
    if (rewardedAd && !isLoading && !isLoaded) {
      setIsLoading(true);
      setError(null);
      rewardedAd.load();
    }
  }, [rewardedAd, isLoading, isLoaded]);

  const showAd = useCallback(async (): Promise<boolean> => {
    try {
      if (!rewardedAd) {
        console.warn('Rewarded ad not initialized');
        return false;
      }

      if (!isLoaded) {
        console.warn('Rewarded ad not loaded yet');
        return false;
      }

      if (!canShowRewarded()) {
        console.log('Rewarded ad cooldown period active');
        return false;
      }

      await rewardedAd.show();
      return true;
    } catch (error) {
      console.error('Failed to show rewarded ad:', error);
      setError(error instanceof Error ? error.message : 'Failed to show ad');
      return false;
    }
  }, [rewardedAd, isLoaded]);

  return {
    isLoaded,
    isLoading,
    error,
    showAd,
    loadAd,
    canShow: canShowRewarded(),
    lastReward,
  };
};

// Hook for premium features unlock
export const usePremiumRewardedAd = (onPremiumUnlocked?: () => void) => {
  const { showAd, isLoaded, canShow, lastReward } = useRewardedAd({
    adLocation: 'premiumFeatures',
    onRewardEarned: (reward) => {
      console.log('Premium features unlocked:', reward);
      onPremiumUnlocked?.();
    },
  });

  const unlockPremiumFeatures = useCallback(async (): Promise<boolean> => {
    if (isLoaded && canShow) {
      return await showAd();
    }
    return false;
  }, [showAd, isLoaded, canShow]);

  return {
    unlockPremiumFeatures,
    isReady: isLoaded && canShow,
    lastReward,
  };
};

// Hook for extra colors unlock
export const useExtraColorsRewardedAd = (onExtraColorsUnlocked?: () => void) => {
  const { showAd, isLoaded, canShow, lastReward } = useRewardedAd({
    adLocation: 'extraColors',
    onRewardEarned: (reward) => {
      console.log('Extra colors unlocked:', reward);
      onExtraColorsUnlocked?.();
    },
  });

  const unlockExtraColors = useCallback(async (): Promise<boolean> => {
    if (isLoaded && canShow) {
      return await showAd();
    }
    return false;
  }, [showAd, isLoaded, canShow]);

  return {
    unlockExtraColors,
    isReady: isLoaded && canShow,
    lastReward,
  };
};

// Hook for managing multiple rewarded ads
export const useMultipleRewardedAds = () => {
  const premiumFeatures = useRewardedAd({ adLocation: 'premiumFeatures' });
  const extraColors = useRewardedAd({ adLocation: 'extraColors' });

  const showPremiumAd = useCallback(async () => {
    return await premiumFeatures.showAd();
  }, [premiumFeatures.showAd]);

  const showExtraColorsAd = useCallback(async () => {
    return await extraColors.showAd();
  }, [extraColors.showAd]);

  return {
    premiumFeatures: {
      ...premiumFeatures,
      showAd: showPremiumAd,
    },
    extraColors: {
      ...extraColors,
      showAd: showExtraColorsAd,
    },
  };
};
