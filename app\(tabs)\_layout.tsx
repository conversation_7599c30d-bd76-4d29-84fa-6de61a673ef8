import { Ionicons } from '@expo/vector-icons';
import { Tabs } from 'expo-router';
import React, { JSX, useEffect, useState } from 'react';
import { Platform, ViewStyle } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuth } from '../../contexts/AuthContext';

export default function TabLayout(): JSX.Element {
    const colorScheme = useColorScheme();
    const { isAuthenticated, user } = useAuth();
    const [forceRender, setForceRender] = useState(0);

    useEffect(() => {
console.log('TabLayout: User data changed, forcing re-render');
// console.log('TabLayout: Previous gender vs current:', user?.gender); // REMOVED: Sensitive gender info
        setForceRender((prev) => prev + 1);
    }, [user?.gender, user?.id, isAuthenticated]);

    useEffect(() => {
        if (user) {
console.log('TabLayout: User object updated, forcing re-render');
            setForceRender((prev) => prev + 1);
        }
    }, [user]);

console.log('TabLayout: Render #', forceRender);
// console.log('TabLayout: isAuthenticated:', isAuthenticated, 'user:', user?.name, 'gender:', user?.gender); // REMOVED: Sensitive user name and gender
console.log('TabLayout: Should show male tab:', user?.gender === 'male');
console.log('TabLayout: Should show female tab:', user?.gender === 'female');

    if (!isAuthenticated) {
        return (
            <Tabs
                screenOptions={{
                    tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
                    headerShown: false,
                    tabBarButton: HapticTab,
                    tabBarBackground: TabBarBackground,
                    tabBarStyle: { display: 'none' },
                }}
            >
                <Tabs.Screen
                    name="index"
                    options={{
                        title: 'Home',
                        tabBarIcon: ({ color }: { color: string }) => (
                            <Ionicons name="home" size={24} color={color} />
                        ),
                    }}
                />
            </Tabs>
        );
    }

    const getTabItemStyle = (isVisible: boolean): ViewStyle => {
        if (!isVisible) {
            return { display: 'none' };
        }
        return {
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
        };
    };

    return (
        <Tabs
            key={`tabs-${user?.gender || 'unknown'}-${forceRender}`}
            screenOptions={{
                tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
                headerShown: false,
                tabBarButton: HapticTab,
                tabBarBackground: TabBarBackground,
                tabBarStyle: Platform.select({
                    ios: {
                        position: 'absolute',
                        display: isAuthenticated ? 'flex' : 'none',
                        justifyContent: 'space-around',
                    },
                    default: {
                        display: isAuthenticated ? 'flex' : 'none',
                        justifyContent: 'space-around',
                    },
                }) as ViewStyle,
            }}
        >
            <Tabs.Screen
                name="index"
                options={{
                    title: 'Home',
                    tabBarIcon: ({ color }: { color: string }) => (
                        <Ionicons name="home" size={24} color={color} />
                    ),
                    tabBarItemStyle: getTabItemStyle(true),
                    href: isAuthenticated ? '/index' : null,
                }}
            />

            <Tabs.Screen
                name="explore"
                options={{
                    title: 'Model',
                    tabBarIcon: ({ color }: { color: string }) => (
                        <Ionicons name="man" size={24} color={color} />
                    ),
                    tabBarButton: user?.gender === 'male' ? undefined : () => null,
                    tabBarItemStyle: getTabItemStyle(user?.gender === 'male'),
                }}
            />

            <Tabs.Screen
                name="female"
                options={{
                    title: 'Model',
                    tabBarIcon: ({ color }: { color: string }) => (
                        <Ionicons name="woman" size={24} color={color} />
                    ),
                    tabBarButton: user?.gender === 'female' ? undefined : () => null,
                    tabBarItemStyle: getTabItemStyle(user?.gender === 'female'),
                }}
            />

            <Tabs.Screen
                name="settings"
                options={{
                    title: 'Settings',
                    tabBarIcon: ({ color }: { color: string }) => (
                        <Ionicons name="settings" size={24} color={color} />
                    ),
                    tabBarItemStyle: getTabItemStyle(true),
                }}
            />
        </Tabs>
    );
}
