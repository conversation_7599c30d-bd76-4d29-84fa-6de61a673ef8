import { ColorCombination } from '../services/colorService';
import { MostRecentRecommendation } from '../services/userRecommendationService';

/**
 * Convert user recommendation to color combination for 3D models
 */
export const convertRecommendationToColorCombination = (
  recommendation: MostRecentRecommendation | null
): ColorCombination | null => {
  try {
    if (!recommendation || !recommendation.hasRecommendations || !recommendation.colorPalette) {
console.log('🎨 RecommendationConverter: No valid recommendation provided');
      return null;
    }

    const { bestColors } = recommendation.colorPalette;
    
    if (!bestColors || bestColors.length === 0) {
console.log('🎨 RecommendationConverter: No best colors in recommendation');
      return null;
    }

    // Create color combination from best colors
    // Use first color for shirt, second for shoes (or fallback to first)
    const shirtColor = bestColors[0] || '#e91e63';
    const shoesColor = bestColors[1] || bestColors[0] || '#000000';

    const colorCombination: ColorCombination = {
      id: `recommendation-${recommendation.recommendation?._id || 'unknown'}`,
      name: 'Your Recommended Colors',
      shirt: shirtColor,
      shoes: shoesColor,
      description: recommendation.colorPalette.generalAdvice || 'Colors recommended based on your face analysis',
      isRecommended: true
    };

console.log('🎨 RecommendationConverter: Created color combination:', {
      id: colorCombination.id,
      shirt: colorCombination.shirt,
      shoes: colorCombination.shoes,
      name: colorCombination.name
    });

    return colorCombination;
  } catch (error) {
console.error('🎨 RecommendationConverter: Error converting recommendation:', error);
    return null;
  }
};

/**
 * Create multiple color combinations from user recommendation
 * Uses different combinations of best colors
 */
export const createMultipleColorCombinationsFromRecommendation = (
  recommendation: MostRecentRecommendation | null
): ColorCombination[] => {
  try {
    if (!recommendation || !recommendation.hasRecommendations || !recommendation.colorPalette) {
console.log('🎨 RecommendationConverter: No valid recommendation for multiple combinations');
      return [];
    }

    const { bestColors } = recommendation.colorPalette;
    
    if (!bestColors || bestColors.length === 0) {
console.log('🎨 RecommendationConverter: No best colors for multiple combinations');
      return [];
    }

    const combinations: ColorCombination[] = [];
    const baseId = recommendation.recommendation?._id || 'unknown';

    // Create combinations using different color pairings
    for (let i = 0; i < Math.min(bestColors.length, 3); i++) {
      const shirtColor = bestColors[i];
      const shoesColor = bestColors[(i + 1) % bestColors.length] || '#000000';

      combinations.push({
        id: `recommendation-${baseId}-${i}`,
        name: `Recommended Style ${i + 1}`,
        shirt: shirtColor,
        shoes: shoesColor,
        description: `Recommended color combination ${i + 1} based on your face analysis`,
        isRecommended: true
      });
    }

console.log('🎨 RecommendationConverter: Created multiple combinations:', combinations.length);
    return combinations;
  } catch (error) {
console.error('🎨 RecommendationConverter: Error creating multiple combinations:', error);
    return [];
  }
};

/**
 * Check if a color combination is from user recommendations
 */
export const isRecommendationBasedCombination = (combination: ColorCombination): boolean => {
  return combination.id?.startsWith('recommendation-') || combination.isRecommended === true;
};

/**
 * Get default color combination when no recommendations available
 */
export const getDefaultColorCombination = (): ColorCombination => {
  return {
    id: 'default',
    name: 'Default Style',
    shirt: '#e91e63',
    shoes: '#000000',
    description: 'Default color combination',
    isRecommended: false
  };
};

export default {
  convertRecommendationToColorCombination,
  createMultipleColorCombinationsFromRecommendation,
  isRecommendationBasedCombination,
  getDefaultColorCombination
};
