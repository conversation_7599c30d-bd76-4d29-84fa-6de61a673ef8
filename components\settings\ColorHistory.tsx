import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    RefreshControl,
    ScrollView,
    StyleSheet,
    Text,
    View
} from 'react-native';
import { apiService } from '../../services/api';

interface ColorPalette {
  analysisId: string;
  colors: string[];
  createdAt: string;
  fileName?: string;
  // Gemini color recommendations data
  colorRecommendations?: {
    bestColors: string[];
    avoidColors: string[];
    seasonalType: string;
    advice: string;
    outfits?: Array<{
      outfitName: string;
      shirt: { color: string; hex: string; reason: string };
      pants: { color: string; hex: string; reason: string };
      shoes: { color: string; hex: string; reason: string };
    }>;
  };
}

export const ColorHistory: React.FC = () => {
  const [colorPalettes, setColorPalettes] = useState<ColorPalette[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Helper function to extract colors from analysis data
  const extractColorsFromAnalysis = (colors: any): string[] => {
    if (!colors || typeof colors !== 'object') {
console.warn('🎨 Invalid colors object:', colors);
      return [];
    }

    const colorArray: string[] = [];

    // Safely extract each color with null checks
    if (colors.skinTone?.hex) {
      colorArray.push(colors.skinTone.hex);
    }
    if (colors.hairColor?.hex) {
      colorArray.push(colors.hairColor.hex);
    }
    if (colors.eyeColor?.hex) {
      colorArray.push(colors.eyeColor.hex);
    }
    if (colors.lipColor?.hex) {
      colorArray.push(colors.lipColor.hex);
    }

    // Filter out invalid colors
    return colorArray.filter(color =>
      color &&
      typeof color === 'string' &&
      color !== '#000000' &&
      color !== '#ffffff' &&
      color.startsWith('#')
    );
  };

  useEffect(() => {
    loadColorHistory();
  }, []);

  const loadColorHistory = async () => {
    try {
      setIsLoading(true);
      // console.log('🎨 Loading color recommendation history...'); // REMOVED: Debug logging

      // Try to get color recommendation history from the new endpoint first
      try {
        const response = await apiService.getColorRecommendationHistory(20);
        // console.log('🎨 Color history response:', response); // REMOVED: Sensitive API response

        if (response.success && response.data?.recommendations && response.data.recommendations.length > 0) {
          // Transform backend response to color palettes
          const palettes: ColorPalette[] = response.data.recommendations.map((recommendation: any) => {
            const faceAnalysis = recommendation.faceAnalysisId;

            return {
              analysisId: recommendation._id,
              colors: faceAnalysis?.colors ? extractColorsFromAnalysis(faceAnalysis.colors) : [],
              createdAt: recommendation.createdAt,
              fileName: faceAnalysis?.originalFileName || 'Unknown file',
              colorRecommendations: {
                bestColors: recommendation.colorPalette?.bestColors || [],
                avoidColors: recommendation.colorPalette?.avoidColors || [],
                seasonalType: recommendation.colorPalette?.seasonalType || 'Unknown',
                advice: recommendation.colorPalette?.generalAdvice || 'No advice available',
                generalAdvice: recommendation.colorPalette?.generalAdvice || 'No advice available',
                outfits: recommendation.recommendations || []
              }
            };
          });

        // console.log('🎨 Transformed color palettes from recommendations:', palettes.length); // REMOVED: Debug logging
          setColorPalettes(palettes);
          return; // Exit early if we got recommendations
        }
      } catch (recommendationError) {
        // console.log('🎨 Color recommendation history not available, falling back to analysis history:', recommendationError); // REMOVED: Debug logging
      }

      // Fallback: Get analysis history if color recommendation history is empty or fails
      // console.log('🎨 Falling back to analysis history...'); // REMOVED: Debug logging
      const analysisResponse = await apiService.getFaceAnalysisHistory();

      if (analysisResponse.success && analysisResponse.data?.analyses) {
        const palettes: ColorPalette[] = analysisResponse.data.analyses
          .filter((analysis: any) => analysis.colors)
          .map((analysis: any) => ({
            analysisId: analysis._id,
            colors: extractColorsFromAnalysis(analysis.colors),
            createdAt: analysis.createdAt,
            fileName: analysis.originalFileName || 'Unknown file',
          }));

        // console.log('🎨 Transformed color palettes from analysis:', palettes.length); // REMOVED: Debug logging
        setColorPalettes(palettes);
      } else {
        // console.log('🎨 No analysis history available'); // REMOVED: Debug logging
        setColorPalettes([]);
      }
    } catch (error: any) {
      console.error('❌ Failed to load color history:', error);
      Alert.alert('Error', 'Failed to load color history: ' + error.message);
      setColorPalettes([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadColorHistory();
    setIsRefreshing(false);
  };

  const renderColorPalette = (palette: ColorPalette, index: number) => {
    return (
      <View key={palette.analysisId} style={styles.paletteCard}>
        <View style={styles.paletteHeader}>
          <View style={styles.paletteHeaderLeft}>
            <Ionicons name="color-palette" size={24} color="#667eea" />
            <View style={styles.paletteHeaderText}>
              <Text style={styles.paletteTitle}>
                Color Palette #{index + 1}
              </Text>
              <Text style={styles.paletteSubtitle}>
                {palette.fileName || 'Unknown file'}
              </Text>
              <Text style={styles.paletteCount}>
                {palette.colors.length} colors detected
              </Text>
            </View>
          </View>
        </View>

        {/* Face Colors Section */}
        {palette.colors.length > 0 ? (
          <View style={styles.colorsContainer}>
            <Text style={styles.sectionTitle}>Detected Face Colors</Text>
            <View style={styles.colorsGrid}>
              {palette.colors.map((color, colorIndex) => (
                <View key={colorIndex} style={styles.colorItem}>
                  <View
                    style={[
                      styles.colorCircle,
                      { backgroundColor: color }
                    ]}
                  />
                  <Text style={styles.colorHex}>{color}</Text>
                </View>
              ))}
            </View>
          </View>
        ) : (
          <View style={styles.noColorsContainer}>
            <Ionicons name="color-palette-outline" size={30} color="#bdc3c7" />
            <Text style={styles.noColorsText}>No colors available</Text>
          </View>
        )}

        {/* Gemini Color Recommendations Section */}
        {palette.colorRecommendations && (
          <View style={styles.recommendationsContainer}>
            {/* Seasonal Type */}
            <View style={styles.seasonalTypeContainer}>
              <Ionicons name="leaf" size={20} color="#27ae60" />
              <Text style={styles.seasonalTypeText}>
                Seasonal Type: {palette.colorRecommendations.seasonalType}
              </Text>
            </View>

            {/* Best Colors */}
            {palette.colorRecommendations.bestColors.length > 0 && (
              <View style={styles.colorSection}>
                <View style={styles.colorSectionHeader}>
                  <Ionicons name="checkmark-circle" size={18} color="#27ae60" />
                  <Text style={styles.colorSectionTitle}>Best Colors for You</Text>
                </View>
                <View style={styles.colorsGrid}>
                  {palette.colorRecommendations.bestColors.map((color, colorIndex) => (
                    <View key={`best-${colorIndex}`} style={styles.colorItem}>
                      <View
                        style={[
                          styles.colorCircle,
                          { backgroundColor: color }
                        ]}
                      />
                      <Text style={styles.colorHex}>{color}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}

            {/* Avoid Colors */}
            {palette.colorRecommendations.avoidColors.length > 0 && (
              <View style={styles.colorSection}>
                <View style={styles.colorSectionHeader}>
                  <Ionicons name="close-circle" size={18} color="#e74c3c" />
                  <Text style={styles.colorSectionTitle}>Colors to Avoid</Text>
                </View>
                <View style={styles.colorsGrid}>
                  {palette.colorRecommendations.avoidColors.map((color, colorIndex) => (
                    <View key={`avoid-${colorIndex}`} style={styles.colorItem}>
                      <View
                        style={[
                          styles.colorCircle,
                          { backgroundColor: color }
                        ]}
                      />
                      <Text style={styles.colorHex}>{color}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}

            {/* AI Advice */}
            <View style={styles.adviceContainer}>
              <View style={styles.adviceHeader}>
                <Ionicons name="bulb" size={18} color="#f39c12" />
                <Text style={styles.adviceTitle}>AI Color Advice</Text>
              </View>
              <Text style={styles.adviceText}>
                {palette.colorRecommendations.advice || palette.colorRecommendations.generalAdvice || 'No advice available'}
              </Text>
            </View>

            {/* Outfit Recommendations */}
            {palette.colorRecommendations.outfits && palette.colorRecommendations.outfits.length > 0 && (
              <View style={styles.outfitsSection}>
                <View style={styles.outfitSectionHeader}>
                  <Ionicons name="shirt" size={18} color="#9b59b6" />
                  <Text style={styles.outfitSectionTitle}>Outfit Suggestions</Text>
                </View>
                {palette.colorRecommendations.outfits.map((outfit, index) => (
                  <View key={index} style={styles.outfitCard}>
                    <Text style={styles.outfitName}>{outfit.outfitName}</Text>
                    <View style={styles.outfitColors}>
                      <View style={styles.outfitColorItem}>
                        <View style={[styles.smallColorCircle, { backgroundColor: outfit.shirt.hex }]} />
                        <Text style={styles.outfitColorText}>Shirt: {outfit.shirt.color}</Text>
                      </View>
                      <View style={styles.outfitColorItem}>
                        <View style={[styles.smallColorCircle, { backgroundColor: outfit.pants.hex }]} />
                        <Text style={styles.outfitColorText}>Pants: {outfit.pants.color}</Text>
                      </View>
                      <View style={styles.outfitColorItem}>
                        <View style={[styles.smallColorCircle, { backgroundColor: outfit.shoes.hex }]} />
                        <Text style={styles.outfitColorText}>Shoes: {outfit.shoes.color}</Text>
                      </View>
                    </View>
                    {outfit.overallReason && (
                      <Text style={styles.outfitReason}>{outfit.overallReason}</Text>
                    )}
                  </View>
                ))}
              </View>
            )}
          </View>
        )}
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#667eea" />
        <Text style={styles.loadingText}>Loading color history...</Text>
      </View>
    );
  }

  return (
    <ScrollView 
      style={styles.container} 
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
      }
    >
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Ionicons name="color-palette" size={40} color="#fff" />
          <Text style={styles.headerTitle}>Color History</Text>
          <Text style={styles.headerSubtitle}>
            {colorPalettes.length} color palette{colorPalettes.length !== 1 ? 's' : ''} found
          </Text>
        </View>
      </LinearGradient>

      <View style={styles.content}>
        {colorPalettes.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="color-palette-outline" size={60} color="#bdc3c7" />
            <Text style={styles.emptyStateTitle}>No Color History</Text>
            <Text style={styles.emptyStateText}>
              You haven't generated any color palettes yet. Perform a face analysis to get personalized color recommendations!
            </Text>
          </View>
        ) : (
          colorPalettes.map((palette, index) => renderColorPalette(palette, index))
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#7f8c8d',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 10,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    marginTop: 5,
  },
  content: {
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  paletteCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  paletteHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  paletteHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  paletteHeaderText: {
    marginLeft: 12,
    flex: 1,
  },
  paletteTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 2,
  },
  paletteSubtitle: {
    fontSize: 14,
    color: '#7f8c8d',
    marginBottom: 2,
  },
  paletteCount: {
    fontSize: 12,
    color: '#95a5a6',
  },
  colorsContainer: {
    borderTopWidth: 1,
    borderTopColor: '#f1f2f6',
    padding: 16,
  },
  colorsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 15,
    justifyContent: 'center',
  },
  colorItem: {
    alignItems: 'center',
    marginBottom: 10,
  },
  colorCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: 8,
    borderWidth: 2,
    borderColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  colorHex: {
    fontSize: 12,
    color: '#2c3e50',
    fontWeight: '500',
    fontFamily: 'monospace',
  },
  noColorsContainer: {
    alignItems: 'center',
    paddingVertical: 30,
    borderTopWidth: 1,
    borderTopColor: '#f1f2f6',
  },
  noColorsText: {
    fontSize: 14,
    color: '#7f8c8d',
    marginTop: 8,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2c3e50',
    marginTop: 20,
    marginBottom: 10,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#7f8c8d',
    textAlign: 'center',
    lineHeight: 24,
  },
  // New styles for Gemini color recommendations
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 12,
  },
  recommendationsContainer: {
    borderTopWidth: 1,
    borderTopColor: '#f1f2f6',
    padding: 16,
    backgroundColor: '#f8f9fa',
  },
  seasonalTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  seasonalTypeText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#27ae60',
    marginLeft: 8,
  },
  colorSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  colorSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  colorSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginLeft: 8,
  },
  adviceContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  adviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  adviceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#f39c12',
    marginLeft: 8,
  },
  adviceText: {
    fontSize: 14,
    color: '#2c3e50',
    lineHeight: 20,
  },
  outfitsSection: {
    marginTop: 15,
  },
  outfitSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  outfitSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginLeft: 8,
  },
  outfitCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  outfitName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 8,
  },
  outfitColors: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  outfitColorItem: {
    alignItems: 'center',
    flex: 1,
  },
  smallColorCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginBottom: 4,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  outfitColorText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  outfitReason: {
    fontSize: 12,
    color: '#777',
    fontStyle: 'italic',
    lineHeight: 16,
  },
});
