import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    RefreshControl,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { apiService, FaceAnalysis } from '../../services/api';

export const AnalysisHistory: React.FC = () => {
  const [analyses, setAnalyses] = useState<FaceAnalysis[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [expandedId, setExpandedId] = useState<string | null>(null);

  useEffect(() => {
    loadAnalysisHistory();
  }, []);

  const loadAnalysisHistory = async () => {
    try {
      setIsLoading(true);
console.log('📊 Loading face analysis history...');

      const response = await apiService.getFaceAnalysisHistory();
console.log('📊 History response:', response);

      if (response.success && response.data?.analyses) {
        // Transform backend response to match frontend format
        const transformedAnalyses = response.data.analyses.map((analysis: any) => ({
          success: true,
          data: {
            _id: analysis._id,
            analysisId: analysis._id, // Normalize for frontend compatibility
            imageUrl: analysis.imageUrl,
            originalFileName: analysis.originalFileName,
            faceDetected: analysis.faceDetected,
            colors: analysis.colors,
            facialFeatures: analysis.facialFeatures,
            faceDimensions: analysis.faceDimensions,
            confidence: analysis.analysisMetadata?.confidence || 0.85,
            processingTime: analysis.analysisMetadata?.processingTime || 0,
            createdAt: analysis.createdAt,
            imageInfo: {
              originalFileName: analysis.originalFileName
            }
          }
        }));

console.log('📊 Transformed analyses:', transformedAnalyses.length);
        setAnalyses(transformedAnalyses);
      } else {
console.warn('📊 Invalid response format:', response);
        setAnalyses([]);
      }
    } catch (error: any) {
console.error('❌ Failed to load analysis history:', error);
      Alert.alert('Error', 'Failed to load analysis history: ' + error.message);
      setAnalyses([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadAnalysisHistory();
    setIsRefreshing(false);
  };

  const handleDeleteAnalysis = (analysisId: string) => {
    Alert.alert(
      'Delete Analysis',
      'Are you sure you want to delete this analysis? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
console.log('🗑️ Deleting analysis:', analysisId);
              await apiService.deleteFaceAnalysis(analysisId);

              // Remove from local state using both _id and analysisId for compatibility
              setAnalyses(prev => prev.filter(analysis =>
                analysis.data._id !== analysisId &&
                analysis.data.analysisId !== analysisId
              ));

              Alert.alert('Success', 'Analysis deleted successfully');
            } catch (error: any) {
              console.error('❌ Failed to delete analysis:', error);
              Alert.alert('Error', 'Failed to delete analysis: ' + error.message);
            }
          },
        },
      ]
    );
  };

  const toggleExpanded = (analysisId: string) => {
    setExpandedId(expandedId === analysisId ? null : analysisId);
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return 'Unknown date';
    }
  };

  const renderAnalysisItem = (analysis: FaceAnalysis, index: number) => {
    const isExpanded = expandedId === analysis.data.analysisId;
    
    return (
      <View key={analysis.data.analysisId || index} style={styles.analysisCard}>
        <TouchableOpacity
          style={styles.analysisHeader}
          onPress={() => toggleExpanded(analysis.data.analysisId)}
        >
          <View style={styles.analysisHeaderLeft}>
            <Ionicons name="analytics" size={24} color="#667eea" />
            <View style={styles.analysisHeaderText}>
              <Text style={styles.analysisTitle}>
                Face Analysis #{index + 1}
              </Text>
              <Text style={styles.analysisDate}>
                {analysis.data.imageInfo?.originalFileName || 'Unknown file'}
              </Text>
              <Text style={styles.analysisSubtitle}>
                Confidence: {(analysis.data.confidence * 100).toFixed(1)}%
              </Text>
            </View>
          </View>
          <View style={styles.analysisHeaderRight}>
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => handleDeleteAnalysis(analysis.data.analysisId)}
            >
              <Ionicons name="trash" size={20} color="#e74c3c" />
            </TouchableOpacity>
            <Ionicons 
              name={isExpanded ? "chevron-up" : "chevron-down"} 
              size={20} 
              color="#999" 
            />
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.analysisDetails}>
            {/* Colors Section */}
            {analysis.data.colors && (
              <View style={styles.detailSection}>
                <Text style={styles.detailSectionTitle}>Detected Colors</Text>
                <View style={styles.colorsGrid}>
                  {analysis.data.colors.skinTone && (
                    <View style={styles.colorItem}>
                      <View style={[styles.colorCircle, { backgroundColor: analysis.data.colors.skinTone.hex || '#fdbcb4' }]} />
                      <Text style={styles.colorName}>Skin</Text>
                      <Text style={styles.colorValue}>{analysis.data.colors.skinTone.primary || 'Unknown'}</Text>
                    </View>
                  )}
                  {analysis.data.colors.hairColor && (
                    <View style={styles.colorItem}>
                      <View style={[styles.colorCircle, { backgroundColor: analysis.data.colors.hairColor.hex || '#4a2c17' }]} />
                      <Text style={styles.colorName}>Hair</Text>
                      <Text style={styles.colorValue}>{analysis.data.colors.hairColor.primary || 'Unknown'}</Text>
                    </View>
                  )}
                  {analysis.data.colors.eyeColor && (
                    <View style={styles.colorItem}>
                      <View style={[styles.colorCircle, { backgroundColor: analysis.data.colors.eyeColor.hex || '#8b4513' }]} />
                      <Text style={styles.colorName}>Eyes</Text>
                      <Text style={styles.colorValue}>{analysis.data.colors.eyeColor.primary || 'Unknown'}</Text>
                    </View>
                  )}
                  {analysis.data.colors.lipColor && (
                    <View style={styles.colorItem}>
                      <View style={[styles.colorCircle, { backgroundColor: analysis.data.colors.lipColor.hex || '#d2691e' }]} />
                      <Text style={styles.colorName}>Lips</Text>
                      <Text style={styles.colorValue}>{analysis.data.colors.lipColor.primary || 'Unknown'}</Text>
                    </View>
                  )}
                </View>
              </View>
            )}

            {/* Features Section */}
            {(analysis.data.features || analysis.data.facialFeatures) && (
              <View style={styles.detailSection}>
                <Text style={styles.detailSectionTitle}>Facial Features</Text>
                <View style={styles.featuresGrid}>
                  <View style={styles.featureItem}>
                    <Text style={styles.featureLabel}>Face Shape</Text>
                    <Text style={styles.featureValue}>
                      {analysis.data.features?.faceShape ||
                       analysis.data.facialFeatures?.faceShape ||
                       'Not detected'}
                    </Text>
                  </View>
                  <View style={styles.featureItem}>
                    <Text style={styles.featureLabel}>Eye Shape</Text>
                    <Text style={styles.featureValue}>
                      {analysis.data.features?.eyeShape ||
                       analysis.data.facialFeatures?.eyeShape ||
                       'Not detected'}
                    </Text>
                  </View>
                  <View style={styles.featureItem}>
                    <Text style={styles.featureLabel}>Nose Shape</Text>
                    <Text style={styles.featureValue}>
                      {analysis.data.features?.noseShape ||
                       analysis.data.facialFeatures?.noseShape ||
                       'Not detected'}
                    </Text>
                  </View>
                  <View style={styles.featureItem}>
                    <Text style={styles.featureLabel}>Lip Shape</Text>
                    <Text style={styles.featureValue}>
                      {analysis.data.features?.lipShape ||
                       analysis.data.facialFeatures?.lipShape ||
                       'Not detected'}
                    </Text>
                  </View>
                </View>
              </View>
            )}

            {/* Processing Info */}
            <View style={styles.detailSection}>
              <Text style={styles.detailSectionTitle}>Processing Info</Text>
              <View style={styles.processingInfo}>
                <Text style={styles.processingText}>
                  Processing Time: {analysis.data.processingTime || 0}ms
                </Text>
                <Text style={styles.processingText}>
                  Algorithm: {analysis.data.metadata?.algorithm || 'Unknown'}
                </Text>
                <Text style={styles.processingText}>
                  Face Detected: {analysis.data.faceDetected ? 'Yes' : 'No'}
                </Text>
                <Text style={styles.processingText}>
                  Confidence: {Math.round((analysis.data.confidence || 0) * 100)}%
                </Text>
              </View>
            </View>
          </View>
        )}
      </View>
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#667eea" />
        <Text style={styles.loadingText}>Loading analysis history...</Text>
      </View>
    );
  }

  return (
    <ScrollView 
      style={styles.container} 
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
      }
    >
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Ionicons name="analytics" size={40} color="#fff" />
          <Text style={styles.headerTitle}>Analysis History</Text>
          <Text style={styles.headerSubtitle}>
            {analyses.length} analysis{analyses.length !== 1 ? 'es' : ''} found
          </Text>
        </View>
      </LinearGradient>

      <View style={styles.content}>
        {analyses.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="analytics-outline" size={60} color="#bdc3c7" />
            <Text style={styles.emptyStateTitle}>No Analysis History</Text>
            <Text style={styles.emptyStateText}>
              You haven't performed any face analysis yet. Upload a photo on the home screen to get started!
            </Text>
          </View>
        ) : (
          analyses.map((analysis, index) => renderAnalysisItem(analysis, index))
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  contentContainer: {
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#7f8c8d',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 10,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    marginTop: 5,
  },
  content: {
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  analysisCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  analysisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  analysisHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  analysisHeaderText: {
    marginLeft: 12,
    flex: 1,
  },
  analysisTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 2,
  },
  analysisDate: {
    fontSize: 14,
    color: '#7f8c8d',
    marginBottom: 2,
  },
  analysisSubtitle: {
    fontSize: 12,
    color: '#95a5a6',
  },
  analysisHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  deleteButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: '#ffeaea',
  },
  analysisDetails: {
    borderTopWidth: 1,
    borderTopColor: '#f1f2f6',
    padding: 16,
  },
  detailSection: {
    marginBottom: 20,
  },
  detailSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 12,
  },
  colorsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  colorItem: {
    alignItems: 'center',
    width: '22%',
  },
  colorCircle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginBottom: 6,
    borderWidth: 2,
    borderColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  colorName: {
    fontSize: 12,
    color: '#7f8c8d',
    marginBottom: 2,
  },
  colorValue: {
    fontSize: 11,
    color: '#2c3e50',
    fontWeight: '500',
    textAlign: 'center',
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  featureItem: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
  },
  featureLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    marginBottom: 4,
  },
  featureValue: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: '600',
  },
  processingInfo: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
  },
  processingText: {
    fontSize: 14,
    color: '#34495e',
    marginBottom: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2c3e50',
    marginTop: 20,
    marginBottom: 10,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#7f8c8d',
    textAlign: 'center',
    lineHeight: 24,
  },
});
