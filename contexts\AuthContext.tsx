import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { apiService, User } from '../services/api';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: {
    name: string;
    email: string;
    password: string;
    gender: 'male' | 'female';
  }) => Promise<{ requiresVerification: boolean; email?: string }>;
  logout: () => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  refreshProfile: (bustCache?: boolean) => Promise<void>;
  changePassword: (passwordData: { currentPassword: string; newPassword: string }) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      console.log('AuthContext: 🔍 Starting authentication check...');

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Authentication check timeout')), 10000); // 10 second timeout
      });

      const authCheckPromise = async () => {
        const authenticated = await apiService.isAuthenticated();
        // console.log('AuthContext: 🔑 Token exists:', authenticated); // REMOVED: Sensitive token info

        if (authenticated) {
          console.log('AuthContext: 👤 Attempting to load user profile...');

          try {
            const userProfile = await apiService.getProfile();
            // console.log('AuthContext: ✅ User profile loaded successfully:', {
            //   id: userProfile.id,
            //   name: userProfile.name,
            //   email: userProfile.email,
            //   gender: userProfile.gender
            // }); // REMOVED: Sensitive user data

            setUser(userProfile);
            setIsAuthenticated(true);
            console.log('AuthContext: 🎉 Authentication successful!');

          } catch (profileError: any) {
            console.error('AuthContext: ❌ Failed to load user profile:', profileError);

            // Handle token expiration specifically
            if (profileError.code === 'TOKEN_EXPIRED') {
              // console.log('AuthContext: 🔐 Token expired, clearing auth state gracefully'); // REMOVED: Sensitive token info

              // Clear auth state immediately - notification will be shown by components
              await apiService.logout();
              setUser(null);
              setIsAuthenticated(false);

              // Store token expiration flag for UI to show message
              try {
                await AsyncStorage.setItem('tokenExpired', 'true');
              } catch (e) {
                // console.log('Failed to store token expiration flag'); // REMOVED: Sensitive storage info
              }

            } else if (profileError.message?.includes('Not authorized') ||
                       profileError.message?.includes('401') ||
                       profileError.message?.includes('Unauthorized')) {
              // console.log('AuthContext: 🚫 Token invalid, clearing auth state'); // REMOVED: Sensitive token info

              await apiService.logout();
              setUser(null);
              setIsAuthenticated(false);

            } else if (profileError.code === 'NETWORK_ERROR') {
              // console.log('AuthContext: 📶 Network error, setting unauthenticated state'); // REMOVED: Debug logging

              setUser(null);
              setIsAuthenticated(false);

            } else {
              // console.log('AuthContext: 🔄 Unknown error, setting unauthenticated state'); // REMOVED: Debug logging
              setUser(null);
              setIsAuthenticated(false);
            }
          }
        } else {
          // console.log('AuthContext: 🔓 No token found, user not authenticated'); // REMOVED: Sensitive token info
          setUser(null);
          setIsAuthenticated(false);
        }
      };

      // Race between auth check and timeout
      await Promise.race([authCheckPromise(), timeoutPromise]);

    } catch (error: any) {
      console.error('AuthContext: 💥 Auth check failed:', error.message);

      if (error.message === 'Authentication check timeout') {
        // console.log('AuthContext: ⏰ Auth check timed out, assuming unauthenticated'); // REMOVED: Debug logging
      }

      // Always set unauthenticated state on any error
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      // console.log('AuthContext: 🏁 Auth check completed, setting loading to false'); // REMOVED: Debug logging
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      // console.log('AuthContext: Starting login for:', email); // REMOVED: Sensitive email info

      const response = await apiService.login({ email, password });
      console.log('AuthContext: Login response:', { success: response.success, hasUser: !!response.user, hasToken: !!response.token });

      if (response.success && response.user && response.token) {
        console.log('AuthContext: Login successful, setting user and authenticated state');

        // Ensure token is stored
        await apiService.setAuthToken(response.token);

        // Set user and authentication state
        // console.log('AuthContext: Setting user data:', {
        //   id: response.user.id,
        //   name: response.user.name,
        //   email: response.user.email,
        //   gender: response.user.gender
        // }); // REMOVED: Sensitive user data
        setUser(response.user);
        setIsAuthenticated(true);

        // Verify the user profile can be loaded
        try {
          const userProfile = await apiService.getProfile();
          // console.log('AuthContext: Profile verification after login:', {
          //   id: userProfile.id,
          //   name: userProfile.name,
          //   email: userProfile.email,
          //   gender: userProfile.gender
          // }); // REMOVED: Sensitive user data
          // console.log('AuthContext: Updating user with profile data'); // REMOVED: Sensitive profile info
          setUser(userProfile); // Use the profile data which might be more complete
        } catch (profileError) {
          console.warn('AuthContext: Profile verification failed after login, using login data:', profileError);
          // Continue with login data if profile fetch fails
        }
      } else {
        throw new Error(response.message || 'Login failed - missing user data or token');
      }
    } catch (error) {
      console.error('AuthContext: Login error:', error);
      // Clear any partial auth state on error
      setUser(null);
      setIsAuthenticated(false);
      await apiService.logout();
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: {
    name: string;
    email: string;
    password: string;
    gender: 'male' | 'female';
  }): Promise<{ requiresVerification: boolean; email?: string }> => {
    try {
      // DON'T set global loading state during registration - let RegisterScreen handle its own loading
      // setIsLoading(true); // Removed to prevent AuthWrapper from showing loading screen
      // console.log('AuthContext: Starting registration with:', { ...userData, password: '[HIDDEN]' }); // REMOVED: Sensitive user data

      const response = await apiService.register(userData);
      console.log('AuthContext: Registration response:', response);

      console.log('AuthContext: Registration response details:', {
        success: response.success,
        hasUser: !!response.user,
        hasToken: !!response.token,
        userGender: response.user?.gender,
        userName: response.user?.name
      });

      if (response.success) {
        console.log('AuthContext: 🔍 Registration successful - ALWAYS requiring OTP verification for new registrations');

        // FOR SIGNUP FLOW: Always require OTP verification regardless of backend response
        // This ensures the flow is: Signup → OTP → Login → Dashboard
        console.log('AuthContext: ✅ Forcing OTP verification flow for all new registrations');

        // Don't auto-authenticate during registration - user must verify email first
        return { requiresVerification: true, email: userData.email };
      }

      console.error('AuthContext: Registration response invalid:', {
        success: response.success,
        hasUser: !!response.user,
        hasToken: !!response.token,
        requiresEmailVerification: response.requiresEmailVerification,
        message: response.message
      });
      throw new Error(response.message || 'Registration failed');
    } catch (error) {
      console.error('AuthContext: Registration error:', error);
      // Clear any partial auth state on error
      setUser(null);
      setIsAuthenticated(false);
      await apiService.logout();
      throw error;
    } finally {
      // DON'T reset global loading state since we didn't set it
      // setIsLoading(false); // Removed to prevent AuthWrapper loading interference
    }
  };

  const logout = async () => {
    try {
      console.log('AuthContext: 🚪 Starting logout process...');
      setIsLoading(true);

      // Clear API token first
      await apiService.logout();
      console.log('AuthContext: ✅ API logout completed');

      // Clear local auth state
      setUser(null);
      setIsAuthenticated(false);
      console.log('AuthContext: ✅ Local auth state cleared');

      // Clear any stored token expiration flags
      try {
        await AsyncStorage.removeItem('tokenExpired');
      } catch (e) {
        // console.log('AuthContext: Failed to clear token expiration flag'); // REMOVED: Sensitive storage info
      }

      console.log('AuthContext: 🎉 Logout completed successfully');

      // Add small delay to prevent navigation conflicts
      await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
      console.error('AuthContext: ❌ Logout error:', error);

      // Even if API logout fails, clear local state
      setUser(null);
      setIsAuthenticated(false);
      console.log('AuthContext: ⚠️ Forced local logout due to error');

      // Add small delay to prevent navigation conflicts
      await new Promise(resolve => setTimeout(resolve, 100));

    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (userData: Partial<User>) => {
    try {
      setIsLoading(true);
      // console.log('AuthContext: Updating profile with data:', userData); // REMOVED: Sensitive user data

      // Update profile via API
      const updatedUser = await apiService.updateProfile(userData);
      console.log('AuthContext: Profile updated successfully:', updatedUser);

      // Update local user state immediately with the response data
      setUser(updatedUser);

      // Force refresh profile from server with cache-busting to get latest data
      // console.log('AuthContext: Force refreshing profile from server (cache-busted) to ensure sync...'); // REMOVED: Sensitive profile info
      try {
        const freshProfile = await apiService.getProfile(true); // true = bust cache
        // console.log('AuthContext: Fresh profile data received:', freshProfile); // REMOVED: Sensitive user data
        setUser(freshProfile);
      } catch (refreshError) {
        console.warn('AuthContext: Cache-busted refresh failed, using update response data:', refreshError);
        // Keep the updated user data from the update response
      }

      // console.log('AuthContext: Profile update and refresh completed'); // REMOVED: Sensitive profile info
    } catch (error) {
      console.error('AuthContext: Profile update error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshProfile = async (bustCache: boolean = false) => {
    try {
      // console.log('AuthContext: Refreshing user profile...', bustCache ? '(cache-busted)' : '(normal)'); // REMOVED: Sensitive profile info
      const userProfile = await apiService.getProfile(bustCache);
      // console.log('AuthContext: Profile refreshed:', {
      //   id: userProfile.id,
      //   name: userProfile.name,
      //   email: userProfile.email,
      //   gender: userProfile.gender
      // }); // REMOVED: Sensitive user data
      setUser(userProfile);
    } catch (error) {
      console.error('AuthContext: Profile refresh error:', error);
      throw error;
    }
  };

  const changePassword = async (passwordData: { currentPassword: string; newPassword: string }) => {
    try {
      setIsLoading(true);
      // console.log('AuthContext: Changing password...'); // REMOVED: Sensitive password operation info

      await apiService.changePassword(passwordData);
      // console.log('AuthContext: Password changed successfully'); // REMOVED: Debug logging
    } catch (error) {
      console.error('AuthContext: Password change error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    updateProfile,
    refreshProfile,
    changePassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
