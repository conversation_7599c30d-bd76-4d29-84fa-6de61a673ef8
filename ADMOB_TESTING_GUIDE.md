# 🧪 AdMob Testing Guide - Complete Setup & Verification

## 🚀 Your AdMob Configuration Summary

**App ID:** `ca-app-pub-4977543488988533~1311624736`

**Ad Units Configured:**
- **Banner Ad:** `ca-app-pub-4977543488988533/9925128841` (Used in all screens)
- **Interstitial Ad:** `ca-app-pub-4977543488988533/4624183785` (Screen transitions)
- **Native Advanced Ad 1:** `ca-app-pub-4977543488988533/9883129763` (Settings screen)
- **Native Advanced Ad 2:** `ca-app-pub-4977543488988533/8612047174` (Home screen)

## 🔧 Step 1: Build and Test

### Clear Cache and Rebuild
```bash
# Clear Metro cache
npx expo start --clear

# Or if that doesn't work, clear everything
rm -rf node_modules
npm install
npx expo start --clear
```

### For Android Testing
```bash
# Build development version
npx expo run:android

# Or use Expo Go
npx expo start
# Then scan QR code with Expo Go app
```

## 📱 Step 2: What to Expect During Testing

### Development Mode (Test Ads)
- **Banner Ads**: Will show "Test Ad" with Google branding
- **Interstitial Ads**: Will show full-screen test ads
- **Native Ads**: Will show test native ad content

### Production Mode (Real Ads)
- Real ads from your AdMob account
- Revenue will be generated
- May take 24-48 hours for ads to start showing consistently

## 🎯 Step 3: Testing Each Ad Type

### Banner Ads Testing
**Locations to check:**
1. **Home Screen** - Bottom of the page
2. **Explore Screen** - Bottom overlay (when viewing 3D models)
3. **Female Screen** - Bottom overlay (when viewing female models)
4. **Settings Screen** - Bottom of settings list

**What to look for:**
- ✅ Ad loads within 3-5 seconds
- ✅ Ad is properly positioned
- ✅ Ad doesn't interfere with app functionality
- ✅ No error messages in console

### Interstitial Ads Testing
**How to trigger:**
1. Go to Home screen
2. Complete face analysis
3. Click "View on 3D Model" button
4. **Expected:** Interstitial ad should show before navigation

**What to look for:**
- ✅ Ad shows full-screen
- ✅ Ad has close button (usually appears after 5 seconds)
- ✅ Navigation continues after ad is closed
- ✅ Frequency control works (won't show again for 1 minute)

### Native Advanced Ads Testing
**Locations to check:**
1. **Home Screen** - Between analysis results and info section
2. **Settings Screen** - Between history and legal sections

**What to look for:**
- ✅ Ad blends naturally with app design
- ✅ Shows headline, description, and call-to-action button
- ✅ Icon and advertiser name display correctly
- ✅ Clicking opens advertiser's content

## 🐛 Step 4: Troubleshooting Common Issues

### Issue: "TurboModuleRegistry.getEnforcing(...): 'RNGoogleMobileAdsModule' could not be found"
**Solution:**
```bash
# Clear everything and reinstall
rm -rf node_modules
npm install
npx expo prebuild --clean
npx expo run:android
```

### Issue: Ads not showing
**Possible causes:**
1. **Network issue** - Check internet connection
2. **Ad inventory** - AdMob might not have ads available
3. **Configuration error** - Double-check ad unit IDs
4. **Development mode** - Test ads should always show

**Debug steps:**
1. Check console for error messages
2. Verify ad unit IDs in `services/admobService.ts`
3. Test with different network (WiFi vs mobile data)

### Issue: App crashes when ads load
**Solution:**
1. Check if you're using the latest version of `react-native-google-mobile-ads`
2. Ensure proper error handling is in place
3. Check device compatibility

## 📊 Step 5: Monitoring Ad Performance

### In Development
- Check console logs for ad events:
  - "Banner ad loaded for home"
  - "Interstitial ad opened for screenTransition"
  - "Native ad loaded for matchWithApp1"

### In Production
1. **AdMob Console** - Check ad performance metrics
2. **Revenue Reports** - Monitor earnings
3. **Fill Rate** - Ensure ads are serving consistently

## 🔍 Step 6: Verification Checklist

### Before Publishing
- [ ] All ad units load without errors
- [ ] Ads don't interfere with app functionality
- [ ] Interstitial ads show at appropriate times
- [ ] Native ads blend well with app design
- [ ] Banner ads are properly positioned
- [ ] No console errors related to ads

### After Publishing
- [ ] Real ads start showing (may take 24-48 hours)
- [ ] Revenue appears in AdMob console
- [ ] User experience remains smooth
- [ ] App store compliance maintained

## 🚨 Important Notes

### Test Device Setup
Add your device ID to test configuration in `services/admobService.ts`:
```typescript
testDeviceIdentifiers: [
  'YOUR_DEVICE_ID_HERE', // Add your actual device ID
],
```

### Ad Frequency
Current settings:
- **Interstitial**: Minimum 1 minute between ads
- **Banner**: Refresh every 30 seconds
- **Native**: Static (no refresh)

### Compliance
- Ensure ads comply with Google AdMob policies
- Don't click your own ads in production
- Follow app store guidelines for ad placement

## 📞 Need Help?

If you encounter issues:
1. Check the console for specific error messages
2. Verify your AdMob account is properly set up
3. Ensure ad units are active in AdMob console
4. Test with different devices/networks

## 🎉 Success Indicators

Your AdMob integration is working correctly when:
- ✅ Test ads show consistently in development
- ✅ No error messages in console
- ✅ App performance remains smooth
- ✅ Ads appear in all designated locations
- ✅ Interstitial ads trigger at correct times
- ✅ Native ads display properly formatted content

Your app is now ready to generate revenue through AdMob! 💰
