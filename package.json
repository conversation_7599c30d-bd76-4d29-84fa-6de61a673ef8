{"name": "my-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "expo": "53.0.19", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-file-system": "~18.1.11", "expo-font": "~13.3.1", "expo-gl": "~15.1.7", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-linking": "~7.1.7", "expo-media-library": "^17.1.7", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-three": "^8.0.0", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-google-mobile-ads": "^15.4.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-toastify": "^11.0.5", "three": "^0.166.1", "three-stdlib": "^2.36.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/three": "^0.177.0", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}