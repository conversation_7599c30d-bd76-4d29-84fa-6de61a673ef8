# 🚀 AdMob Integration Configuration Guide

## 📋 Overview
Your app now has complete AdMob integration with banner ads, interstitial ads, and native advanced ads. All your actual AdMob IDs have been configured and the app is ready to use!

## ✅ Your AdMob Configuration (COMPLETED)

### Your App ID
- **Android App ID**: `ca-app-pub-4977543488988533~1311624736` ✅

### Your Ad Units (CONFIGURED)
- **Banner Ad**: `ca-app-pub-4977543488988533/9925128841` ✅
- **Interstitial Ad**: `ca-app-pub-4977543488988533/4624183785` ✅
- **Native Advanced Ad 1**: `ca-app-pub-4977543488988533/9883129763` ✅
- **Native Advanced Ad 2**: `ca-app-pub-4977543488988533/8612047174` ✅

All your AdMob IDs have been configured in the app!

## 🎯 Step 2: Configure App IDs

### File: `app.json`
```json
{
  "expo": {
    "plugins": [
      [
        "expo-ads-admob",
        {
          "androidAppId": "ca-app-pub-YOUR_ANDROID_APP_ID~YOUR_APP_ID",
          "iosAppId": "ca-app-pub-YOUR_IOS_APP_ID~YOUR_APP_ID"
        }
      ]
    ]
  }
}
```

**🔥 REPLACE:**
- `YOUR_ANDROID_APP_ID` with your actual Android App ID
- `YOUR_IOS_APP_ID` with your actual iOS App ID

## 🎯 Step 3: Configure Ad Unit IDs

### File: `services/admobService.ts`

Replace ALL the placeholder IDs in the `ADMOB_CONFIG` object:

```typescript
export const ADMOB_CONFIG = {
  // 🔥 REPLACE THESE WITH YOUR ACTUAL ADMOB IDS 🔥
  APP_ID: {
    android: 'ca-app-pub-YOUR_ANDROID_APP_ID~YOUR_APP_ID',
    ios: 'ca-app-pub-YOUR_IOS_APP_ID~YOUR_APP_ID',
  },
  
  // Banner Ad Unit IDs
  BANNER_ADS: {
    home: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_BANNER_AD_UNIT_ID_1',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_BANNER_AD_UNIT_ID_1',
    },
    explore: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_BANNER_AD_UNIT_ID_2',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_BANNER_AD_UNIT_ID_2',
    },
    female: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_BANNER_AD_UNIT_ID_3',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_BANNER_AD_UNIT_ID_3',
    },
    settings: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_BANNER_AD_UNIT_ID_4',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_BANNER_AD_UNIT_ID_4',
    },
  },
  
  // Interstitial Ad Unit IDs
  INTERSTITIAL_ADS: {
    screenTransition: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_INTERSTITIAL_AD_UNIT_ID_1',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_INTERSTITIAL_AD_UNIT_ID_1',
    },
    modelView: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_INTERSTITIAL_AD_UNIT_ID_2',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_INTERSTITIAL_AD_UNIT_ID_2',
    },
  },
  
  // Rewarded Ad Unit IDs
  REWARDED_ADS: {
    premiumFeatures: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_REWARDED_AD_UNIT_ID_1',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_REWARDED_AD_UNIT_ID_1',
    },
    extraColors: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_REWARDED_AD_UNIT_ID_2',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_REWARDED_AD_UNIT_ID_2',
    },
  },
};
```

## 📱 Step 4: Ad Placement Summary

### Banner Ads (Always Visible)
- **Home Screen**: Bottom of ScrollView
- **Explore Screen**: Bottom overlay (absolute positioning)
- **Female Screen**: Bottom overlay (absolute positioning)  
- **Settings Screen**: Bottom of ScrollView

### Interstitial Ads (Shown Strategically)
- **Screen Transition**: When navigating from home to 3D models
- **Model View**: When switching between different 3D models

### Rewarded Ads (User-Initiated)
- **Premium Features**: Unlock premium color combinations
- **Extra Colors**: Unlock additional color palettes

## 🧪 Step 5: Testing

### Development Mode
- App automatically uses test ad unit IDs during development
- Test ads will show instead of real ads
- No revenue generated from test ads

### Production Mode
- Real ads will show when app is built for production
- Revenue will be generated from real ad impressions

## 🔧 Step 6: Build and Deploy

### For Development Testing:
```bash
expo start
```

### For Production Build:
```bash
# Android
expo build:android

# iOS  
expo build:ios
```

## 📊 Step 7: Monitor Performance

1. Check AdMob console for ad performance
2. Monitor fill rates and eCPM
3. Adjust ad frequency if needed in `admobService.ts`

## ⚙️ Advanced Configuration

### Ad Frequency Settings
In `services/admobService.ts`, you can adjust:
```typescript
export const AD_FREQUENCY = {
  INTERSTITIAL_MIN_INTERVAL: 60000, // 1 minute between interstitial ads
  BANNER_REFRESH_INTERVAL: 30000,   // 30 seconds banner refresh
  REWARDED_COOLDOWN: 300000,        // 5 minutes cooldown for rewarded ads
};
```

### Test Device IDs
Add your test device IDs in `admobService.ts`:
```typescript
testDeviceIdentifiers: [
  'YOUR_TEST_DEVICE_ID_1',
  'YOUR_TEST_DEVICE_ID_2',
],
```

## 🎉 You're All Set!

Your app now has complete AdMob integration. Just replace the placeholder IDs with your actual AdMob IDs and you'll start earning revenue from ads!

## 📞 Need Help?

If you need help getting your AdMob IDs or have questions about the integration, feel free to ask!
