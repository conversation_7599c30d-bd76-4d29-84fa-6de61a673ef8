// Utility functions for safe data access to prevent TypeError exceptions

/**
 * Safely access nested object properties
 */
export const safeGet = (obj: any, path: string, defaultValue: any = null) => {
  try {
    if (!obj || typeof obj !== 'object') {
      return defaultValue;
    }
    
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : defaultValue;
    }, obj);
  } catch (error) {
    // console.warn('🛡️ SafeGet error for path:', path, error); // REMOVED: Debug logging
    return defaultValue;
  }
};

/**
 * Safely access analysis colors with fallbacks
 */
export const safeGetColors = (analysisData: any) => {
  const colors = safeGet(analysisData, 'colors', {});
  
  return {
    skinTone: {
      primary: safeGet(colors, 'skinTone.primary', 'Medium'),
      hex: safeGet(colors, 'skinTone.hex', '#fdbcb4'),
      rgb: safeGet(colors, 'skinTone.rgb', { r: 253, g: 188, b: 180 }),
      confidence: safeGet(colors, 'skinTone.confidence', 0.85)
    },
    hairColor: {
      primary: safeGet(colors, 'hairColor.primary', 'Brown'),
      hex: safeGet(colors, 'hairColor.hex', '#4a2c17'),
      rgb: safeGet(colors, 'hairColor.rgb', { r: 74, g: 44, b: 23 }),
      confidence: safeGet(colors, 'hairColor.confidence', 0.85)
    },
    eyeColor: {
      primary: safeGet(colors, 'eyeColor.primary', 'Brown'),
      hex: safeGet(colors, 'eyeColor.hex', '#8b4513'),
      rgb: safeGet(colors, 'eyeColor.rgb', { r: 139, g: 69, b: 19 }),
      confidence: safeGet(colors, 'eyeColor.confidence', 0.85)
    },
    lipColor: {
      primary: safeGet(colors, 'lipColor.primary', 'Natural'),
      hex: safeGet(colors, 'lipColor.hex', '#d2691e'),
      rgb: safeGet(colors, 'lipColor.rgb', { r: 210, g: 105, b: 30 }),
      confidence: safeGet(colors, 'lipColor.confidence', 0.85)
    }
  };
};

/**
 * Safely access facial features with fallbacks
 */
export const safeGetFeatures = (analysisData: any) => {
  const features = safeGet(analysisData, 'features', {}) || safeGet(analysisData, 'facialFeatures', {});
  
  return {
    faceShape: safeGet(features, 'faceShape', 'Not detected'),
    eyeShape: safeGet(features, 'eyeShape', 'Not detected'),
    eyeDistance: safeGet(features, 'eyeDistance', 'Not detected'),
    eyebrowShape: safeGet(features, 'eyebrowShape', 'Not detected'),
    noseShape: safeGet(features, 'noseShape', 'Not detected'),
    lipShape: safeGet(features, 'lipShape', 'Not detected')
  };
};

/**
 * Safely access face dimensions with fallbacks
 */
export const safeGetDimensions = (analysisData: any) => {
  const dimensions = safeGet(analysisData, 'dimensions', {}) || safeGet(analysisData, 'faceDimensions', {});
  
  return {
    faceWidth: safeGet(dimensions, 'faceWidth', 0),
    faceHeight: safeGet(dimensions, 'faceHeight', 0) || safeGet(dimensions, 'faceLength', 0),
    lengthToWidthRatio: safeGet(dimensions, 'lengthToWidthRatio', 0),
    jawWidth: safeGet(dimensions, 'jawWidth', 0),
    foreheadWidth: safeGet(dimensions, 'foreheadWidth', 0),
    cheekboneWidth: safeGet(dimensions, 'cheekboneWidth', 0),
    jawToForeheadRatio: safeGet(dimensions, 'jawToForeheadRatio', 0),
    cheekboneToJawRatio: safeGet(dimensions, 'cheekboneToJawRatio', 0)
  };
};

/**
 * Safely access analysis metadata
 */
export const safeGetMetadata = (analysisData: any) => {
  const metadata = safeGet(analysisData, 'metadata', {}) || safeGet(analysisData, 'analysisMetadata', {});
  
  return {
    processingTime: safeGet(analysisData, 'processingTime', 0) || safeGet(metadata, 'processingTime', 0),
    confidence: safeGet(analysisData, 'confidence', 0.85) || safeGet(metadata, 'confidence', 0.85),
    algorithm: safeGet(metadata, 'algorithm', 'Unknown'),
    errors: safeGet(metadata, 'errors', []),
    warnings: safeGet(metadata, 'warnings', [])
  };
};

/**
 * Safely check if analysis has valid data
 */
export const hasValidAnalysisData = (analysisData: any): boolean => {
  try {
    return !!(
      analysisData &&
      typeof analysisData === 'object' &&
      (analysisData.faceDetected === true || analysisData.faceDetected === 'true') &&
      (analysisData.colors || analysisData.features || analysisData.facialFeatures || analysisData.dimensions || analysisData.faceDimensions)
    );
  } catch (error) {
console.warn('🛡️ Error checking analysis data validity:', error);
    return false;
  }
};

/**
 * Safely format confidence percentage
 */
export const safeFormatConfidence = (confidence: any): string => {
  try {
    const conf = parseFloat(confidence);
    if (isNaN(conf)) return '0%';
    return `${Math.round(conf * 100)}%`;
  } catch (error) {
    return '0%';
  }
};

/**
 * Safely format processing time
 */
export const safeFormatProcessingTime = (processingTime: any): string => {
  try {
    const time = parseInt(processingTime);
    if (isNaN(time)) return '0ms';
    return `${time}ms`;
  } catch (error) {
    return '0ms';
  }
};

/**
 * Safely extract color hex values for color combinations
 */
export const safeExtractColorHexes = (colors: any): string[] => {
  try {
    const safeColors = safeGetColors({ colors });
    return [
      safeColors.skinTone.hex,
      safeColors.hairColor.hex,
      safeColors.eyeColor.hex,
      safeColors.lipColor.hex
    ].filter(hex => hex && hex !== '#000000' && hex !== '#ffffff');
  } catch (error) {
    // console.warn('🛡️ Error extracting color hexes:', error); // REMOVED: Debug logging
    return ['#fdbcb4', '#4a2c17', '#8b4513', '#d2691e']; // Default colors
  }
};

/**
 * Safely validate and normalize analysis response
 */
export const normalizeAnalysisResponse = (response: any) => {
  try {
    if (!response || typeof response !== 'object') {
      throw new Error('Invalid response object');
    }

    const data = response.data || response;
    
    return {
      success: response.success !== false,
      data: {
        _id: data._id || data.analysisId || data.id,
        analysisId: data._id || data.analysisId || data.id,
        imageUrl: data.imageUrl || '',
        originalFileName: data.originalFileName || 'Unknown file',
        faceDetected: data.faceDetected === true || data.faceDetected === 'true',
        colors: safeGetColors(data),
        features: safeGetFeatures(data),
        facialFeatures: safeGetFeatures(data),
        dimensions: safeGetDimensions(data),
        faceDimensions: safeGetDimensions(data),
        metadata: safeGetMetadata(data),
        confidence: safeGet(data, 'confidence', 0.85),
        processingTime: safeGet(data, 'processingTime', 0),
        createdAt: data.createdAt || new Date().toISOString(),
        imageInfo: {
          originalFileName: data.originalFileName || 'Unknown file'
        }
      }
    };
  } catch (error) {
console.error('🛡️ Error normalizing analysis response:', error);
    return {
      success: false,
      error: error.message,
      data: null
    };
  }
};
