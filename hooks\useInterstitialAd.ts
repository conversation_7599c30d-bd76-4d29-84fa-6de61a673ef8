import { AdMobInterstitial } from 'expo-ads-admob';
import { useCallback, useEffect, useState } from 'react';
import {
    ADMOB_CONFIG,
    canShowInterstitial,
    getAdUnitIdForEnvironment,
    markInterstitialShown
} from '../services/admobService';

interface UseInterstitialAdProps {
  adLocation: 'screenTransition' | 'modelView';
  autoLoad?: boolean;
}

interface UseInterstitialAdReturn {
  isLoaded: boolean;
  isLoading: boolean;
  error: string | null;
  showAd: () => Promise<boolean>;
  loadAd: () => void;
  canShow: boolean;
}

export const useInterstitialAd = ({
  adLocation,
  autoLoad = true
}: UseInterstitialAdProps): UseInterstitialAdReturn => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [adUnitId, setAdUnitId] = useState<string>('');

  // Initialize the interstitial ad
  useEffect(() => {
    const adConfig = ADMOB_CONFIG.INTERSTITIAL_ADS[adLocation];
    const unitId = getAdUnitIdForEnvironment(adConfig, 'interstitial');
    setAdUnitId(unitId);

    if (unitId && autoLoad) {
      loadAd();
    }
  }, [adLocation, autoLoad]);

  const loadAd = useCallback(async () => {
    if (!adUnitId || isLoading || isLoaded) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      await AdMobInterstitial.setAdUnitID(adUnitId);
      await AdMobInterstitial.requestAdAsync({ servePersonalizedAds: true });

      setIsLoaded(true);
      setIsLoading(false);
      console.log(`Interstitial ad loaded for ${adLocation}`);
    } catch (error) {
      setIsLoaded(false);
      setIsLoading(false);
      setError(error instanceof Error ? error.message : 'Failed to load interstitial ad');
      console.warn(`Interstitial ad error for ${adLocation}:`, error);
    }
  }, [adUnitId, isLoading, isLoaded, adLocation]);

  const showAd = useCallback(async (): Promise<boolean> => {
    try {
      if (!adUnitId) {
        console.warn('Interstitial ad not initialized');
        return false;
      }

      if (!isLoaded) {
        console.warn('Interstitial ad not loaded yet');
        return false;
      }

      if (!canShowInterstitial()) {
        console.log('Interstitial ad frequency limit reached');
        return false;
      }

      await AdMobInterstitial.showAdAsync();
      console.log(`Interstitial ad shown for ${adLocation}`);

      markInterstitialShown();
      setIsLoaded(false);

      // Auto-reload the ad for next time
      if (autoLoad) {
        setTimeout(() => loadAd(), 1000);
      }

      return true;
    } catch (error) {
      console.error('Failed to show interstitial ad:', error);
      setError(error instanceof Error ? error.message : 'Failed to show ad');
      return false;
    }
  }, [adUnitId, isLoaded, adLocation, autoLoad, loadAd]);

  return {
    isLoaded,
    isLoading,
    error,
    showAd,
    loadAd,
    canShow: canShowInterstitial(),
  };
};

// Hook for showing interstitial ads with automatic frequency management
export const useInterstitialAdWithFrequency = (adLocation: 'screenTransition' | 'modelView') => {
  const { showAd, isLoaded, canShow } = useInterstitialAd({ adLocation });

  const showAdIfReady = useCallback(async (): Promise<boolean> => {
    if (isLoaded && canShow) {
      return await showAd();
    }
    return false;
  }, [showAd, isLoaded, canShow]);

  return {
    showAdIfReady,
    isReady: isLoaded && canShow,
  };
};

// Hook for preloading multiple interstitial ads
export const useMultipleInterstitialAds = () => {
  const screenTransition = useInterstitialAd({ adLocation: 'screenTransition' });
  const modelView = useInterstitialAd({ adLocation: 'modelView' });

  const showScreenTransitionAd = useCallback(async () => {
    return await screenTransition.showAd();
  }, [screenTransition.showAd]);

  const showModelViewAd = useCallback(async () => {
    return await modelView.showAd();
  }, [modelView.showAd]);

  return {
    screenTransition: {
      ...screenTransition,
      showAd: showScreenTransitionAd,
    },
    modelView: {
      ...modelView,
      showAd: showModelViewAd,
    },
  };
};
