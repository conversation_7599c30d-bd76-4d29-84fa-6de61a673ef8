import { useCallback, useEffect, useState } from 'react';
import {
    ADMOB_CONFIG,
    canShowInterstitial,
    getAdUnitIdForEnvironment,
    markInterstitialShown
} from '../services/admobService';

interface UseInterstitialAdProps {
  adLocation: 'screenTransition' | 'modelView';
  autoLoad?: boolean;
}

interface UseInterstitialAdReturn {
  isLoaded: boolean;
  isLoading: boolean;
  error: string | null;
  showAd: () => Promise<boolean>;
  loadAd: () => void;
  canShow: boolean;
}

export const useInterstitialAd = ({
  adLocation,
  autoLoad = true
}: UseInterstitialAdProps): UseInterstitialAdReturn => {
  const [interstitial, setInterstitial] = useState<InterstitialAd | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize the interstitial ad
  useEffect(() => {
    const adConfig = ADMOB_CONFIG.INTERSTITIAL_ADS[adLocation];
    const adUnitId = getAdUnitIdForEnvironment(adConfig, 'interstitial');

    if (adUnitId) {
      const interstitialAd = InterstitialAd.createForAdRequest(adUnitId, {
        requestNonPersonalizedAdsOnly: false,
        keywords: ['fashion', 'style', 'colors', 'clothing'],
      });

      setInterstitial(interstitialAd);

      // Set up event listeners
      const unsubscribeLoaded = interstitialAd.addAdEventListener(
        AdEventType.LOADED,
        () => {
          setIsLoaded(true);
          setIsLoading(false);
          setError(null);
          console.log(`Interstitial ad loaded for ${adLocation}`);
        }
      );

      const unsubscribeError = interstitialAd.addAdEventListener(
        AdEventType.ERROR,
        (error) => {
          setIsLoaded(false);
          setIsLoading(false);
          setError(error.message || 'Failed to load interstitial ad');
          console.warn(`Interstitial ad error for ${adLocation}:`, error);
        }
      );

      const unsubscribeOpened = interstitialAd.addAdEventListener(
        AdEventType.OPENED,
        () => {
          console.log(`Interstitial ad opened for ${adLocation}`);
        }
      );

      const unsubscribeClosed = interstitialAd.addAdEventListener(
        AdEventType.CLOSED,
        () => {
          console.log(`Interstitial ad closed for ${adLocation}`);
          markInterstitialShown();
          setIsLoaded(false);
          // Auto-reload the ad for next time
          if (autoLoad) {
            loadAd();
          }
        }
      );

      // Auto-load if enabled
      if (autoLoad) {
        loadAd();
      }

      // Cleanup function
      return () => {
        unsubscribeLoaded();
        unsubscribeError();
        unsubscribeOpened();
        unsubscribeClosed();
      };
    }
  }, [adLocation, autoLoad]);

  const loadAd = useCallback(() => {
    if (interstitial && !isLoading && !isLoaded) {
      setIsLoading(true);
      setError(null);
      interstitial.load();
    }
  }, [interstitial, isLoading, isLoaded]);

  const showAd = useCallback(async (): Promise<boolean> => {
    try {
      if (!interstitial) {
        console.warn('Interstitial ad not initialized');
        return false;
      }

      if (!isLoaded) {
        console.warn('Interstitial ad not loaded yet');
        return false;
      }

      if (!canShowInterstitial()) {
        console.log('Interstitial ad frequency limit reached');
        return false;
      }

      await interstitial.show();
      return true;
    } catch (error) {
      console.error('Failed to show interstitial ad:', error);
      setError(error instanceof Error ? error.message : 'Failed to show ad');
      return false;
    }
  }, [interstitial, isLoaded]);

  return {
    isLoaded,
    isLoading,
    error,
    showAd,
    loadAd,
    canShow: canShowInterstitial(),
  };
};

// Hook for showing interstitial ads with automatic frequency management
export const useInterstitialAdWithFrequency = (adLocation: 'screenTransition' | 'modelView') => {
  const { showAd, isLoaded, canShow } = useInterstitialAd({ adLocation });

  const showAdIfReady = useCallback(async (): Promise<boolean> => {
    if (isLoaded && canShow) {
      return await showAd();
    }
    return false;
  }, [showAd, isLoaded, canShow]);

  return {
    showAdIfReady,
    isReady: isLoaded && canShow,
  };
};

// Hook for preloading multiple interstitial ads
export const useMultipleInterstitialAds = () => {
  const screenTransition = useInterstitialAd({ adLocation: 'screenTransition' });
  const modelView = useInterstitialAd({ adLocation: 'modelView' });

  const showScreenTransitionAd = useCallback(async () => {
    return await screenTransition.showAd();
  }, [screenTransition.showAd]);

  const showModelViewAd = useCallback(async () => {
    return await modelView.showAd();
  }, [modelView.showAd]);

  return {
    screenTransition: {
      ...screenTransition,
      showAd: showScreenTransitionAd,
    },
    modelView: {
      ...modelView,
      showAd: showModelViewAd,
    },
  };
};
