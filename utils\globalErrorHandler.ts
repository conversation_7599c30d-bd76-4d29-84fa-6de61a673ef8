import { errorHandlingService } from '../services/errorHandlingService';
import { notificationService } from '../services/notificationService';

export interface GlobalErrorOptions {
  showNotification?: boolean;
  context?: string;
  fallbackMessage?: string;
  onTokenExpired?: () => void;
  onNetworkError?: () => void;
}

class GlobalErrorHandler {
  private static instance: GlobalErrorHandler;
  private tokenExpiredCallback?: () => void;

  static getInstance(): GlobalErrorHandler {
    if (!GlobalErrorHandler.instance) {
      GlobalErrorHandler.instance = new GlobalErrorHandler();
    }
    return GlobalErrorHandler.instance;
  }

  // Set global callback for token expiration
  setTokenExpiredCallback(callback: () => void) {
    this.tokenExpiredCallback = callback;
  }

  // Handle any error globally
  handleError(error: any, options: GlobalErrorOptions = {}): void {
    const {
      showNotification = true,
      context = 'Unknown',
      fallbackMessage,
      onTokenExpired,
      onNetworkError
    } = options;

console.error(`🚨 Global Error Handler [${context}]:`, error);

    // Get error information
    const errorInfo = errorHandlingService.getErrorInfo(error);

    // Handle specific error types
    switch (errorInfo.code) {
      case 'TOKEN_EXPIRED':
        this.handleTokenExpiration(error, onTokenExpired);
        break;

      case 'NETWORK_ERROR':
        this.handleNetworkError(error, onNetworkError);
        break;

      case 'NO_FACE_DETECTED':
        if (showNotification) {
          notificationService.noFaceDetected();
        }
        break;

      case 'VALIDATION_ERROR':
        if (showNotification) {
          notificationService.error(
            errorInfo.funnyMessage,
            errorInfo.solution
          );
        }
        break;

      default:
        if (showNotification) {
          notificationService.error(
            fallbackMessage || errorInfo.funnyMessage,
            errorInfo.solution
          );
        }
        break;
    }
  }

  private handleTokenExpiration(error: any, callback?: () => void): void {
console.log('🔐 Global Error Handler: Token expired, handling gracefully');

    // Show user-friendly notification
    notificationService.error(
      '⏰ Session expire ho gaya! Dobara login kar bhai! 🔄',
      'Your session has expired. Please login again to continue.'
    );

    // Call specific callback if provided
    if (callback) {
      callback();
    }

    // Call global callback if set
    if (this.tokenExpiredCallback) {
      this.tokenExpiredCallback();
    }
  }

  private handleNetworkError(error: any, callback?: () => void): void {
console.log('📶 Global Error Handler: Network error, handling gracefully');

    // Show network error notification
    notificationService.networkError(
      '📶 Arre yaar, internet bhi nahi hai! WiFi check kar le! 📡'
    );

    // Call specific callback if provided
    if (callback) {
      callback();
    }
  }

  // Wrapper for async functions to handle errors automatically
  async wrapAsync<T>(
    asyncFn: () => Promise<T>,
    options: GlobalErrorOptions = {}
  ): Promise<T | null> {
    try {
      return await asyncFn();
    } catch (error) {
      this.handleError(error, options);
      return null;
    }
  }

  // Wrapper for API calls specifically
  async wrapApiCall<T>(
    apiCall: () => Promise<T>,
    context: string = 'API Call',
    options: Partial<GlobalErrorOptions> = {}
  ): Promise<T | null> {
    return this.wrapAsync(apiCall, {
      context,
      showNotification: true,
      ...options
    });
  }

  // Create a safe version of any function
  makeSafe<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    context: string,
    options: Partial<GlobalErrorOptions> = {}
  ): T {
    return (async (...args: any[]) => {
      return this.wrapAsync(
        () => fn(...args),
        { context, ...options }
      );
    }) as T;
  }
}

// Export singleton instance
export const globalErrorHandler = GlobalErrorHandler.getInstance();

// Convenience functions
export const handleError = (error: any, options?: GlobalErrorOptions) => {
  globalErrorHandler.handleError(error, options);
};

export const wrapApiCall = <T>(
  apiCall: () => Promise<T>,
  context?: string,
  options?: Partial<GlobalErrorOptions>
) => {
  return globalErrorHandler.wrapApiCall(apiCall, context, options);
};

export const makeSafeApiCall = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  context: string,
  options?: Partial<GlobalErrorOptions>
) => {
  return globalErrorHandler.makeSafe(fn, context, options);
};

// Set up global token expiration handler
export const setGlobalTokenExpiredHandler = (callback: () => void) => {
  globalErrorHandler.setTokenExpiredCallback(callback);
};
