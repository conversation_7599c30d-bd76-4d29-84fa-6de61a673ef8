import { useAuth } from "@/contexts/AuthContext";
import { Ionicons } from '@expo/vector-icons';
import { useGLTF } from '@react-three/drei';
import { Canvas, useFrame } from '@react-three/fiber';
import { Asset } from 'expo-asset';
import React, { Suspense, useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Animated, Dimensions, PanResponder, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SvgXml } from 'react-native-svg';
import {
  Color,
  LinearFilter,
  MeshLambertMaterial,
  MeshStandardMaterial,
  RepeatWrapping
} from 'three';
import SimpleModel from '../../components/SimpleModel';
import { ColorRecommendation } from '../../services/api';
import { ColorCombination } from '../../services/colorService';



const { width, height } = Dimensions.get('window');

// Enhanced error suppression - completely fix trim error
const suppressSpecificErrors = () => {
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;
    const originalConsoleLog = console.log;

    console.error = (...args) => {
        try {
            // Safely process arguments
            const safeArgs = args || [];
            const message = safeArgs
                .filter(arg => arg !== null && arg !== undefined)
                .map(arg => {
                    try {
                        if (typeof arg === 'string') return arg;
                        if (typeof arg === 'number') return String(arg);
                        if (typeof arg === 'boolean') return String(arg);
                        if (typeof arg === 'object') return '[Object]';
                        return String(arg);
                    } catch (e) {
                        return '[Unknown]';
                    }
                })
                .join(' ');

            // Only suppress specific texture loading errors, not all errors
            if (
                message && (
                    message.includes('THREE.TextureLoader: Error loading') ||
                    message.includes('Failed to load texture') ||
                    message.includes('Image load error') ||
                    message.includes('GLTFLoader') ||
                    message.includes('Couldn\'t load texture') ||
                    message.includes('ArrayBuffer') ||
                    message.includes('blob') ||
                    message.includes('trim')
                )
            ) {
                return; // Completely suppress these errors
            }
        } catch (e) {
            // If any error processing fails, just pass through original
        }
        originalConsoleError(...args);
    };

    console.warn = (...args) => {
        try {
            const safeArgs = args || [];
            const message = safeArgs
                .filter(arg => arg !== null && arg !== undefined)
                .map(arg => {
                    try {
                        if (typeof arg === 'string') return arg;
                        return String(arg);
                    } catch (e) {
                        return '[Unknown]';
                    }
                })
                .join(' ');

            if (
                message && (
                    message.includes('THREE.TextureLoader') ||
                    message.includes('texture') ||
                    message.includes('GLTFLoader') ||
                    message.includes('trim')
                )
            ) {
                return; // Completely suppress these warnings
            }
        } catch (e:any) {
            // If error processing fails, just pass through
        }
        originalConsoleWarn(...args);
    };

    console.log = (...args) => {
        try {
            const safeArgs = args || [];
            const message = safeArgs
                .filter(arg => arg !== null && arg !== undefined)
                .map(arg => {
                    try {
                        if (typeof arg === 'string') return arg;
                        return String(arg);
                    } catch (e) {
                        return '[Unknown]';
                    }
                })
                .join(' ');

            if (
                message && (
                    message.includes('EXGL: gl.pixelStorei()') ||
                    message.includes('pixelStorei') ||
                    message.includes('doesn\'t support this parameter yet') ||
                    message.includes('gl.pixelStorei') ||
                    message.includes('trim')
                )
            ) {
                return; // Suppress pixelStorei warnings completely
            }
        } catch (e) {
            // If error processing fails, just pass through
        }
        originalConsoleLog(...args);
    };
};

suppressSpecificErrors();

// Enhanced Model component with gesture controls and proper texture loading
function Model3D({
    rotationX,
    rotationY,
    isManualRotation,
    colorCombination
}: {
    rotationX: number,
    rotationY: number,
    isManualRotation: boolean,
    colorCombination?: ColorCombination
}) {
    const [modelUri, setModelUri] = useState<string | null>(null);
    const [useSimpleModel, setUseSimpleModel] = useState(false);
    const [modelProcessed, setModelProcessed] = useState(false);
    const [texturesLoaded, setTexturesLoaded] = useState(false);
    const groupRef = useRef<any>(null);
    const autoRotationRef = useRef(0);
    const gltf = modelUri ? useGLTF(modelUri) : null;

    // Load model URI
    useEffect(() => {
        const loadModelUri = async () => {
            try {
                // Try direct require first
                try {
                    const directUri = require('../../assets/models/temp.glb');
                    setModelUri(directUri);
                    return;
                } catch (err) {
                    // Try Asset.fromModule as fallback
                }

                // Try Asset.fromModule
                try {
                    const modelAsset = Asset.fromModule(require('../../assets/models/temp.glb'));
                    await modelAsset.downloadAsync();
                    const uri = modelAsset.localUri || modelAsset.uri;

                    if (uri) {
                        setModelUri(uri);
                        return;
                    }
                } catch (err) {
                    // Asset loading failed
                }

                // Fallback to simple model after 5 seconds
                setTimeout(() => {
                    setUseSimpleModel(true);
                }, 5000);

            } catch (error) {
                setUseSimpleModel(true);
            }
        };

        loadModelUri();
    }, []);

    // Process the loaded model with better texture handling
    useEffect(() => {
        if (gltf && gltf.scene && !modelProcessed) {
            // console.log('✅ Processing GLTF model...'); // REMOVED: Debug logging
            // console.log('📊 Model info:', {
            //     animations: gltf.animations?.length || 0,
            //     materials: Object.keys(gltf.materials || {}).length,
            //     nodes: Object.keys(gltf.nodes || {}).length
            // }); // REMOVED: Model structure data

            setModelProcessed(true);

            // Process materials with enhanced texture loading
            gltf.scene.traverse((child: any) => {
                if (child.isMesh && child.material) {
                    const materials = Array.isArray(child.material) ? child.material : [child.material];

                    materials.forEach((material: any, index: number) => {
                        try {
                            const materialName = material?.name || 'unnamed';
                            const childName = child?.name || '';
                            // console.log(`🎨 Processing material ${index}:`, materialName); // REMOVED: Material processing logs

                            // Enhanced material processing with mesh name context
                            processMaterialWithTextures(material, child, index, childName);
                        } catch (error) {
                            console.error('❌ Error processing material:', error);
                            // Apply fallback solid color
                            try {
                                applyFallbackMaterial(child, material, index, child?.name || '');
                            } catch (fallbackError) {
console.error('❌ Fallback material also failed:', fallbackError);
                            }
                        }
                    });

                    // Optimize for mobile performance
                    child.castShadow = false;
                    child.receiveShadow = false;
                }
            });

            // console.log('✅ Model processing complete'); // REMOVED: Debug logging
            setTexturesLoaded(true);
        }
    }, [gltf, modelProcessed]);

    // Reprocess materials when color combination changes
    useEffect(() => {
        if (gltf && gltf.scene && modelProcessed && colorCombination) {
            // console.log('🎨 Updating model colors with new combination:', colorCombination); // REMOVED: Sensitive color combination data

            gltf.scene.traverse((child: any) => {
                if (child.isMesh && child.material) {
                    const materials = Array.isArray(child.material) ? child.material : [child.material];

                    materials.forEach((material: any, index: number) => {
                        const materialName = material?.name || 'unnamed';
                        const childName = child?.name || '';

                        // Update color based on new combination
                        const newColor = getIntelligentColor(materialName, index, childName);
                        if (material.color) {
                            material.color.copy(newColor);
                            material.needsUpdate = true;
                        }
                    });
                }
            });
        }
    }, [colorCombination, gltf, modelProcessed]);

    // Enhanced material processing function
    const processMaterialWithTextures = (material: any, child: any, index: number, meshName: string) => {
        // Create enhanced material with better properties
        const enhancedMaterial = new MeshStandardMaterial({
            name: material.name || `enhanced_material_${index}`,
        });

        // Copy basic material properties
        if (material.color) enhancedMaterial.color = material.color.clone();
        if (material.emissive) enhancedMaterial.emissive = material.emissive.clone();
        if (material.roughness !== undefined) enhancedMaterial.roughness = material.roughness;
        if (material.metalness !== undefined) enhancedMaterial.metalness = material.metalness;
        if (material.opacity !== undefined) enhancedMaterial.opacity = material.opacity;
        if (material.transparent !== undefined) enhancedMaterial.transparent = material.transparent;

        // Handle textures with fallbacks
        if (material.map) {
            try {
                // Try to use existing texture
                enhancedMaterial.map = material.map;
                if (enhancedMaterial.map) {
                    enhancedMaterial.map.wrapS = RepeatWrapping;
                    enhancedMaterial.map.wrapT = RepeatWrapping;
                    enhancedMaterial.map.minFilter = LinearFilter;
                    enhancedMaterial.map.magFilter = LinearFilter;
                }
                // console.log(`✅ Applied existing texture to material ${index} (${meshName})`); // REMOVED: Material processing logs
            } catch (textureError) {
                // console.log(`⚠️ Texture application failed for material ${index}, using solid color`); // REMOVED: Material processing logs
                // Apply intelligent solid color based on material name AND mesh name
                enhancedMaterial.color = getIntelligentColor(material.name || '', index, meshName);
            }
        } else {
            // No texture available, use intelligent solid color
            enhancedMaterial.color = getIntelligentColor(material.name || '', index, meshName);
            // console.log(`🎨 Applied solid color to material ${index} (${meshName}):`, enhancedMaterial.color.getHexString()); // REMOVED: Color application logs
        }

        // Handle normal maps
        if (material.normalMap) {
            try {
                enhancedMaterial.normalMap = material.normalMap;
                enhancedMaterial.normalScale.set(0.5, 0.5); // Reduce normal intensity for mobile
            } catch (error) {
                // console.log(`⚠️ Normal map failed for material ${index}`); // REMOVED: Material processing logs
            }
        }

        // Apply mobile-optimized settings
        enhancedMaterial.roughness = enhancedMaterial.roughness || 0.8;
        enhancedMaterial.metalness = enhancedMaterial.metalness || 0.1;

        // Force material update
        enhancedMaterial.needsUpdate = true;

        // Apply the enhanced material
        if (Array.isArray(child.material)) {
            child.material[index] = enhancedMaterial;
        } else {
            child.material = enhancedMaterial;
        }

        // console.log(`✅ Enhanced material ${index} applied successfully to ${meshName}`); // REMOVED: Material processing logs
    };

    // Fallback material application
    const applyFallbackMaterial = (child: any, originalMaterial: any, index: number, meshName: string) => {
        const fallbackMaterial = new MeshLambertMaterial({
            color: getIntelligentColor(originalMaterial.name || '', index, meshName),
            transparent: false,
        });

        if (Array.isArray(child.material)) {
            child.material[index] = fallbackMaterial;
        } else {
            child.material = fallbackMaterial;
        }

        // console.log(`🔧 Applied fallback material ${index} to ${meshName}`); // REMOVED: Material processing logs
    };

    // Intelligent color assignment based on your specific model parts
    const getIntelligentColor = (materialName: string, index: number, meshName: string = ''): Color => {
        const safeMaterialName = materialName || '';
        const safeMeshName = meshName || '';
        const name = safeMaterialName.toLowerCase();
        const mesh = safeMeshName.toLowerCase();

        // console.log(`🎨 Assigning color for material: "${safeMaterialName}" on mesh: "${safeMeshName}"`); // REMOVED: Color assignment logs

        // Use color combination if available
        if (colorCombination) {
            // Apply shirt color to sweater/shirt meshes
            if (mesh.includes('ch31_sweater') || mesh.includes('sweater') ||
                name.includes('ch31_sweater') || name.includes('sweater') ||
                mesh.includes('shirt') || name.includes('shirt')) {
                // console.log('   → Applied shirt color from combination:', colorCombination.shirt); // REMOVED: Color application logs
                return new Color(colorCombination.shirt);
            }

            // Apply pants color to pants meshes
            if (mesh.includes('ch31_pants') || mesh.includes('pants') ||
                name.includes('ch31_pants') || name.includes('pants')) {
                // console.log('   → Applied pants color from combination:', colorCombination.pants); // REMOVED: Color application logs
                return new Color(colorCombination.pants);
            }

            // Apply shoes color to shoes meshes
            if (mesh.includes('ch31_shoes') || mesh.includes('shoes') ||
                name.includes('ch31_shoes') || name.includes('shoes')) {
                // console.log('   → Applied shoes color from combination:', colorCombination.shoes); // REMOVED: Color application logs
                return new Color(colorCombination.shoes);
            }
        }

        // Default colors for body parts (not affected by color combinations)
        if (mesh.includes('ch31_body') || mesh.includes('body')) {
            // console.log('   → Applied skin tone for body mesh'); // REMOVED: Color application logs
            return new Color(0xfdbcb4); // Natural skin tone
        }
        if (mesh.includes('ch31_hair') || mesh.includes('hair')) {
            // console.log('   → Applied brown color for hair mesh'); // REMOVED: Color application logs
            return new Color(0x4a2c17); // Dark brown hair
        }
        if (mesh.includes('ch31_eyelashes') || mesh.includes('eyelash')) {
            // console.log('   → Applied dark color for eyelashes mesh'); // REMOVED: Color application logs
            return new Color(0x2c1810); // Very dark brown for eyelashes
        }
        if (mesh.includes('ch31_collar') || mesh.includes('collar')) {
            // console.log('   → Applied white color for collar mesh'); // REMOVED: Color application logs
            return new Color(0xffffff); // Pure white collar
        }

        // Fallback colors when no color combination is available
        if (mesh.includes('ch31_pants') || mesh.includes('pants')) {
            // console.log('   → Applied default blue color for pants mesh'); // REMOVED: Color application logs
            return new Color(0x1e40af); // Blue jeans color
        }
        if (mesh.includes('ch31_shoes') || mesh.includes('shoes')) {
            // console.log('   → Applied default dark color for shoes mesh'); // REMOVED: Color application logs
            return new Color(0x1f2937); // Dark shoe color
        }
        if (mesh.includes('ch31_sweater') || mesh.includes('sweater')) {
            // console.log('   → Applied default red color for sweater mesh'); // REMOVED: Color application logs
            return new Color(0xe11d48); // Vibrant red sweater
        }

        // Then check material name
        if (name.includes('ch31_body') || name.includes('body')) {
            // console.log('   → Applied skin tone for body material'); // REMOVED: Color application logs
            return new Color(0xfdbcb4); // Natural skin tone
        }
        if (name.includes('ch31_hair') || name.includes('hair')) {
            // console.log('   → Applied brown color for hair material'); // REMOVED: Color application logs
            return new Color(0x4a2c17); // Dark brown hair
        }
        if (name.includes('ch31_eyelashes') || name.includes('eyelash')) {
            // console.log('   → Applied dark color for eyelashes material'); // REMOVED: Color application logs
            return new Color(0x2c1810); // Very dark brown for eyelashes
        }
        if (name.includes('ch31_pants') || name.includes('pants')) {
            // console.log('   → Applied blue color for pants material'); // REMOVED: Color application logs
            return new Color(0x1e40af); // Blue jeans color
        }
        if (name.includes('ch31_shoes') || name.includes('shoes')) {
            // console.log('   → Applied dark color for shoes material'); // REMOVED: Color application logs
            return new Color(0x1f2937); // Dark shoe color
        }
        if (name.includes('ch31_sweater') || name.includes('sweater')) {
            // console.log('   → Applied red color for sweater material'); // REMOVED: Color application logs
            return new Color(0xdc2626); // Red sweater
        }
        if (name.includes('ch31_collar') || name.includes('collar')) {
            // console.log('   → Applied white color for collar material'); // REMOVED: Color application logs
            return new Color(0xf8fafc); // White collar
        }

        // General fallback colors
        if (name.includes('skin') || name.includes('body') || name.includes('face')) {
            return new Color(0xfdbcb4); // Natural skin tone
        }
        if (name.includes('hair')) {
            return new Color(0x4a2c17); // Dark brown hair
        }
        if (name.includes('eye')) {
            return new Color(0x2e4057); // Dark blue eyes
        }
        if (name.includes('cloth') || name.includes('shirt') || name.includes('dress')) {
            return new Color(0x1e3a8a); // Navy blue clothing
        }
        if (name.includes('pants') || name.includes('trouser')) {
            return new Color(0x374151); // Dark gray pants
        }
        if (name.includes('shoe') || name.includes('boot')) {
            return new Color(0x1f2937); // Dark shoe color
        }
        if (name.includes('metal') || name.includes('button')) {
            return new Color(0x9ca3af); // Metallic gray
        }
        if (name.includes('leather') || name.includes('belt')) {
            return new Color(0x92400e); // Brown leather
        }

        // Generate consistent color based on material name hash
        let hash = 0;
        const hashString = safeMaterialName || `material_${index}`;
        for (let i = 0; i < hashString.length; i++) {
            hash = ((hash << 5) - hash + hashString.charCodeAt(i)) & 0xffffffff;
        }

        // Generate pleasant colors (avoid pure black/white)
        const hue = Math.abs(hash) % 360;
        const saturation = 40 + (Math.abs(hash >> 8) % 40); // 40-80%
        const lightness = 30 + (Math.abs(hash >> 16) % 40); // 30-70%

        return new Color().setHSL(hue / 360, saturation / 100, lightness / 100);
    };

    // Enhanced rotation with gesture control
    useFrame((state, delta) => {
        if (groupRef.current) {
            // Only manual rotation - no auto-rotation
            groupRef.current.rotation.y = rotationY;
            groupRef.current.rotation.x = rotationX;
        }
    });

    // Return simple model if needed with same rotation props
    if (useSimpleModel) {
        // console.log('🎨 Using SimpleModel fallback'); // REMOVED: Debug logging
        return <SimpleModel rotationX={rotationX} rotationY={rotationY} isManualRotation={isManualRotation} />;
    }

    // Show SVG loading state
    if (!gltf || !modelProcessed) {
        return null; // Let the SVG loader handle the loading state outside Canvas
    }

    // Render the processed model with slightly reduced size
    return (
        <group ref={groupRef} scale={[1.6, 1.6, 1.6]} position={[0, -1.5, 0]}>
            <primitive object={gltf.scene} />
        </group>
    );
}

// Enhanced SVG Loading component with scanning effect - no rotation
function SvgLoader() {
    const scaleValue = useRef(new Animated.Value(1)).current;
    const scanLinePosition = useRef(new Animated.Value(0)).current;
    const scanOpacity = useRef(new Animated.Value(0.8)).current;

    useEffect(() => {
        // Gentle breathing/pulsing animation (very subtle)
        const pulseAnimation = Animated.loop(
            Animated.sequence([
                Animated.timing(scaleValue, {
                    toValue: 1.02,
                    duration: 2000,
                    useNativeDriver: true,
                }),
                Animated.timing(scaleValue, {
                    toValue: 0.98,
                    duration: 2000,
                    useNativeDriver: true,
                }),
            ])
        );

        // Scanning line animation - moves from top to bottom
        const scanAnimation = Animated.loop(
            Animated.sequence([
                Animated.timing(scanLinePosition, {
                    toValue: 1,
                    duration: 2500,
                    useNativeDriver: true,
                }),
                Animated.timing(scanLinePosition, {
                    toValue: 0,
                    duration: 500,
                    useNativeDriver: true,
                }),
                Animated.delay(1000), // Pause between scans
            ])
        );

        // Scanning opacity pulse
        const scanOpacityAnimation = Animated.loop(
            Animated.sequence([
                Animated.timing(scanOpacity, {
                    toValue: 1,
                    duration: 1000,
                    useNativeDriver: true,
                }),
                Animated.timing(scanOpacity, {
                    toValue: 0.3,
                    duration: 1000,
                    useNativeDriver: true,
                }),
            ])
        );

        pulseAnimation.start();
        scanAnimation.start();
        scanOpacityAnimation.start();

        return () => {
            pulseAnimation.stop();
            scanAnimation.stop();
            scanOpacityAnimation.stop();
        };
    }, []);

    const scanLineY = scanLinePosition.interpolate({
        inputRange: [0, 1],
        outputRange: [0, 320], // Height of SVG
    });

    // Simplified SVG content based on your original-image.svg with human body outline

    // here is my svg of loading
    const svgContent = `<svg viewBox="0 0 660.46 1206.46" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-screen lg:w-full mc:h-[700px] 2xl:h-[90vh] 3xl:h-[95vh] 4xl:h-screen sm:mx-auto object-fit  animate-fade-in menor"><defs><radialGradient id="jointradial" cx="50%" cy="50%" r="50%" fx="50%" fy="50%"><stop offset="0%" style="stop-color: rgb(254, 91, 127); stop-opacity: 1;"></stop><stop offset="100%" style="stop-color: rgb(231, 236, 239); stop-opacity: 1;"></stop></radialGradient></defs><g id="calves" class="bodymap text-mw-gray active:text-mw-red-700 lg:hover:text-mw-red-100"><path d="M502.8,1183.5c-.68,1.05-1.86,1.29-2.74,1.31-.93.02-1.69.81-1.69,1.77,0,.38-.14,1.54-.78,2.18-.39.38-.97.56-1.75.53-.8-.04-1.51.52-1.72,1.32,0,.03-.6,2.33-2.27,3.3-.86.51-1.88.59-3.12.24-.69-.19-1.44.07-1.86.67-.02.03-1.86,2.61-4.39,2.9-1.39.17-2.76-.38-4.19-1.68-.68-.62-1.71-.58-2.35.08-.04.04-4.49,4.53-10.15,3.77-4.52-.61-8.73-4.34-12.51-11.09-.21-.39-.58-.69-1.02-.81-.57-.17-14.2-4.29-13.15-17.37.18-5.53-4.76-8.41-11.01-12.05l-.97-.57c-5.92-3.45-9.83-5.73-6.12-27.69.89-7.14-.42-14.69-.48-15-.11-.62-.33-1.05-.9-1.25-.42-.36-3.52-3.52-2.29-17.55,1.51-17.34,2.94-33.72-17.75-101.36-1.11-3.85-2.68-6.08-4.18-8.24-4.08-5.83-8.31-11.86-2.32-56.42,2.35-21.34,3.14-29.8,2.5-34.69,6.67,6.59,14.23,10.26,21.63,10.26h.34c8.38-.13,15.5-4.85,20.06-13.29,4.38-8.1,7.01-11.48,12.38-13.17.31.97.72,2.16,1.23,3.63,5.67,16.3,20.73,59.59,8.3,131.92,0,.05,0,.1-.02.15-.7,7.93-1.67,16.17-2.6,24.14-2.43,20.85-4.73,40.55-2.42,53.28,1.72,10.5,2.43,14.98,2.6,20.5,0,.15.03.3.07.45,1.34,4.55,8.23,15.73,12.79,23.12,1.33,2.14,2.46,4,3.13,5.14.8,1.37,1.22,2.38,1.59,3.25,1.09,2.59,1.89,4.14,6.89,8.26.9.74,1.83,1.49,2.8,2.26,6.46,5.2,13.78,11.1,17.75,20.07,1.41,3.47,1.65,6.21.69,7.71Z" fill="currentColor"></path><path d="M265.06,986.92c-1.51,2.16-3.08,4.4-4.18,8.22-20.71,67.67-19.28,84.05-17.76,101.38,1.22,14.03-1.87,17.2-2.29,17.55-.56.2-.79.63-.9,1.25-.05.31-1.37,7.86-.47,15.08,3.7,21.87-.21,24.15-6.13,27.61l-.98.57c-6.25,3.64-11.18,6.51-10.99,12.13,1.04,12.99-12.58,17.12-13.16,17.28-.44.12-.8.42-1.02.81-3.78,6.75-7.99,10.47-12.51,11.09-5.67.76-10.1-3.72-10.15-3.77-.63-.66-1.67-.7-2.35-.08-1.42,1.29-2.8,1.84-4.18,1.68-2.53-.29-4.39-2.88-4.41-2.9-.42-.6-1.15-.87-1.85-.67-1.25.35-2.28.27-3.13-.24-1.67-.98-2.27-3.28-2.27-3.3-.2-.8-.91-1.36-1.72-1.33-.79.03-1.36-.15-1.75-.53-.64-.64-.78-1.79-.78-2.17,0-.96-.74-1.76-1.68-1.77-.88-.02-2.07-.26-2.75-1.31-.96-1.5-.72-4.24.67-7.66,4-9.02,11.31-14.92,17.78-20.12.96-.77,1.89-1.52,2.79-2.26,5-4.13,5.8-5.67,6.89-8.26.37-.88.79-1.88,1.59-3.25.67-1.15,1.81-2.99,3.12-5.13,4.56-7.4,11.46-18.58,12.8-23.13.04-.15.07-.3.07-.45.17-5.52.88-10,2.6-20.47,2.31-12.77,0-32.47-2.42-53.32-.93-7.96-1.9-16.21-2.6-24.14,0-.05,0-.1-.02-.15-12.43-72.33,2.63-115.62,8.3-131.92.51-1.47.92-2.66,1.23-3.63,5.38,1.69,8,5.06,12.39,13.17,4.55,8.44,11.67,13.16,20.05,13.29h.35c7.39,0,14.95-3.67,21.62-10.27-.64,4.9.15,13.37,2.5,34.74,5.99,44.51,1.77,50.55-2.31,56.38Z" fill="currentColor"></path></g><g id="quads" class="bodymap text-mw-gray active:text-mw-red-700 lg:hover:text-mw-red-100"><path d="M452.4,783.8s0,.06-.02.09c0,.02,0,.04,0,.06-.8,3.72-1.52,7.5-2.22,11.15-3.01,15.71-5.86,30.55-13.07,34.21-2.93,1.49-6.55,1.09-11.09-1.18-2.16-1.08-4.06-1.25-5.66-.47-3.13,1.52-4.12,5.95-5.38,11.57-2.08,9.29-4.43,19.83-16.56,20.04h-.25c-22.01,0-29.18-50.54-29.24-51.05,0-.05-.02-.1-.03-.14-2.46-18.26-6.39-36.59-11.76-54.7-1.1-3.72-2.91-8.81-5.01-14.71-6.53-18.38-16.39-46.18-17.75-68.65-.11-3.62-.17-6.84-.23-9.67-.15-7.05-.24-11.52-.79-14.17,14.83-2.06,21.68-20.6,33.8-62.45,13.65-47.17,49.14-60.08,62.35-63.31,2.7,21.79,6.16,45.47,8.19,53.68.14.55.33,1.32.59,2.31,9.41,36.36,27.37,122.24,14.14,207.42Z" fill="currentColor"></path><path d="M327.13,646.17c-.55,2.65-.64,7.12-.79,14.17-.05,2.83-.12,6.06-.22,9.62-1.36,22.53-11.23,50.32-17.75,68.7-2.1,5.9-3.9,11-5.01,14.71-5.37,18.13-9.32,36.48-11.77,54.76,0,.03-.02.05-.02.08-.08.52-7.24,51.05-29.25,51.05h-.25c-12.12-.21-14.48-10.74-16.56-20.04-1.26-5.61-2.25-10.04-5.38-11.57-1.59-.78-3.49-.61-5.66.47-4.54,2.27-8.16,2.66-11.09,1.18-7.21-3.66-10.06-18.5-13.07-34.21-.71-3.71-1.44-7.54-2.26-11.3v-.03c-13.22-85.17,4.75-171.04,14.15-207.39.26-.99.45-1.76.59-2.31,2.03-8.2,5.5-31.89,8.2-53.68,13.22,3.23,48.69,16.15,62.34,63.31,12.12,41.86,18.97,60.39,33.8,62.45Z" fill="currentColor"></path></g><g id="abdominals" class="bodymap text-mw-gray active:text-mw-red-700 lg:hover:text-mw-red-100"><path d="M385.82,350.44c-2.32-5.36-9.35-9.32-16.39-12.32-.03-.02-.05-.03-.08-.03-1.6-.57-3.18-1.2-4.75-1.9-.03,0-.06-.02-.09-.04-10.51-3.96-23.74-6.74-29.44-2.69-1.99,1.42-2.95,3.62-2.95,6.73,0,.97-.77,1.75-1.71,1.75-.06,0-.12,0-.17,0-.06,0-.12,0-.18,0-.94,0-1.71-.78-1.71-1.75,0-3.11-.97-5.32-2.95-6.73-5.7-4.06-18.93-1.27-29.44,2.69-.03.02-.06.03-.09.04-1.57.7-3.15,1.33-4.75,1.9-.03.02-.07.03-.11.04-7.03,3-14.03,6.96-16.36,12.31-1.47,3.38-.66,6.05.81,10.88,1.22,4.02,2.84,9.3,3.56,17.09,0,0,0,.03,0,.04.11.8.17,1.63.21,2.49.03.67.08,1.35.1,2.06.1,2.61.04,3.85-.03,5.54-.09,1.82-.21,4.31-.15,9.95.09,11.41,2.3,60.52,4.34,93.24,3.28,52.64,34.75,92.8,46.58,92.8.06,0,.12,0,.18,0,.05,0,.11,0,.17,0,11.83,0,43.3-40.16,46.58-92.8,2.05-32.73,4.24-81.84,4.34-93.24.05-5.64-.07-8.13-.15-9.95-.08-1.7-.14-2.93-.03-5.54.03-.71.07-1.39.1-2.06.03-.87.09-1.7.21-2.49,0-.02,0-.04,0-.04.73-7.79,2.34-13.07,3.56-17.09,1.47-4.83,2.28-7.5.81-10.88ZM332.12,571.92c0,.97-.77,1.75-1.71,1.75h-.35c-.94,0-1.71-.78-1.71-1.75v-67.35c0-.96.77-1.75,1.71-1.75h.35c.94,0,1.71.79,1.71,1.75v67.35ZM361.89,479.09c-5.77-.12-20.94,1.39-26.17,2.61-2.83.66-3.58,3.45-3.77,7.75.05,1.27.05,2.54.05,3.74v.79c0,.96-.77,1.75-1.71,1.75h-.11c-.94,0-1.71-.79-1.71-1.75v-.79c0-1.21,0-2.47.05-3.74-.19-4.29-.94-7.09-3.76-7.75-5.24-1.22-20.4-2.73-26.18-2.61h-.03c-.93,0-1.69-.75-1.71-1.7-.02-.97.73-1.77,1.68-1.79,6.09-.13,21.47,1.41,27,2.69,2.28.53,3.77,1.79,4.73,3.46.95-1.67,2.44-2.93,4.72-3.46,5.54-1.29,20.94-2.82,27-2.69.95.03,1.69.82,1.68,1.79-.02.96-.81,1.77-1.75,1.7ZM369.89,472.83c-5.48.19-9.3.47-12.68.72-4.2.31-7.74.56-12.89.56-1.67,0-3.5-.03-5.59-.09-4.64-.35-7.15-1.87-8.5-5.2-1.34,3.33-3.83,4.84-8.43,5.19-2.13.06-4,.1-5.7.1-5.14,0-8.67-.26-12.86-.56-3.38-.24-7.2-.52-12.68-.72-.94-.04-1.69-.85-1.65-1.81.03-.97.8-1.73,1.77-1.69,5.54.2,9.39.48,12.8.73,5.48.39,9.8.71,18.14.46,5.19-.4,6.72-1.24,6.72-17.55,0-.96.77-1.75,1.71-1.75.06,0,.12,0,.18,0,.05,0,.11,0,.17,0,.94,0,1.71.79,1.71,1.75,0,16.31,1.52,17.15,6.79,17.55,8.27.24,12.59-.07,18.06-.46,3.41-.24,7.26-.52,12.8-.73h.06c.92,0,1.68.74,1.71,1.69.03.96-.71,1.77-1.65,1.81ZM298.34,426.66c5.55-.66,14.83-1.75,23.1-1.63,4.85.07,7.43,2,8.8,4.75,1.36-2.74,3.93-4.68,8.79-4.75,8.27-.11,17.55.98,23.1,1.63.94.11,1.62.98,1.51,1.94-.11.96-.97,1.64-1.9,1.54-5.47-.65-14.61-1.73-22.66-1.62-5.95.08-6.95,3.1-6.95,12.13,0,.97-.77,1.75-1.71,1.75-.06,0-.12,0-.17,0-.06,0-.12,0-.18,0-.94,0-1.71-.78-1.71-1.75,0-9.03-1-12.06-6.95-12.13-8.03-.11-17.2.97-22.66,1.62-.93.11-1.79-.58-1.9-1.54-.1-.96.56-1.83,1.51-1.94ZM371.36,425.01l-1.44.03c-3.47.06-6.9.11-10.21.11-11.14,0-21.01-.65-26.98-4.11-1.08-.62-1.89-1.43-2.5-2.4-.62.96-1.43,1.77-2.51,2.4-5.97,3.46-15.84,4.11-26.98,4.11-3.31,0-6.73-.05-10.21-.11l-1.44-.03c-.94,0-1.69-.8-1.69-1.77.02-.95.78-1.72,1.71-1.72h.03l1.44.03c14.67.24,28.5.48,35.44-3.54,1.7-.99,2.32-3.08,2.47-6.24-.08-1.48-.05-3.05,0-4.7-.02-.52-.03-1.08-.05-1.64-.06-1.77-.13-3.58-.13-5.5,0-.96.77-1.75,1.71-1.75.06,0,.12,0,.18,0,.05,0,.11,0,.17,0,.94,0,1.71.79,1.71,1.75,0,1.91-.06,3.74-.13,5.5-.02.52-.03,1.02-.04,1.5.05,1.73.08,3.39,0,4.94.17,3.1.79,5.17,2.47,6.15,6.94,4.02,20.78,3.79,35.44,3.54l1.44-.03h.03c.93,0,1.69.77,1.71,1.72,0,.97-.74,1.77-1.69,1.77ZM298.11,377.35l1.21-.32c5.42-1.47,14.52-3.92,22.23-2.57,4.77.83,7.32,2.92,8.69,5.58,1.36-2.66,3.92-4.75,8.68-5.58,7.72-1.35,16.8,1.1,22.23,2.57l1.21.32c.92.24,1.47,1.2,1.23,2.13-.24.94-1.17,1.49-2.09,1.25l-1.22-.32c-5.16-1.4-13.79-3.72-20.78-2.51-6.27,1.09-7.38,4.37-7.38,10.86,0,.96-.77,1.75-1.71,1.75-.06,0-.12,0-.17,0-.06,0-.12,0-.18,0-.94,0-1.71-.79-1.71-1.75,0-6.49-1.1-9.77-7.38-10.86-6.99-1.22-15.62,1.12-20.78,2.51l-1.22.32c-.92.24-1.85-.31-2.09-1.25-.23-.94.32-1.89,1.23-2.13ZM375.7,377.49c-.21.76-.9,1.26-1.64,1.26-.15,0-.32-.02-.48-.07-13.9-4.14-23.32-6.63-37.09-6.36-3.26-.02-5.15-1.85-6.25-4.72-1.1,2.87-2.99,4.7-6.23,4.72-13.79-.28-23.22,2.22-37.12,6.36-.16.05-.33.07-.48.07-.74,0-1.43-.5-1.64-1.26-.27-.93.26-1.9,1.16-2.17,14.22-4.23,23.88-6.78,38.11-6.5,4.01-.02,4.31-6.78,4.31-17.11,0-.97.77-1.75,1.71-1.75.06,0,.12,0,.18,0,.05,0,.11,0,.17,0,.94,0,1.71.78,1.71,1.75,0,10.32.3,17.09,4.35,17.11,14.19-.29,23.85,2.26,38.07,6.5.91.27,1.43,1.24,1.16,2.17Z" fill="currentColor"></path></g><g id="obliques" class="bodymap text-mw-gray active:text-mw-red-700 lg:hover:text-mw-red-100"><path d="M272.8,406.06c-2.74,15.58-5.85,33.23-3.49,49.66,4.83,33.57-6.55,48.85-11.65,53.86-6.22,6.09-16.8,8.99-26.23,7.31,1.27-10.42,2.32-20.17,2.91-27.28.42-5.01,1.09-10.04,1.79-15.35,2.58-19.47,5.24-39.6-4.39-59.14-19.23-37.82-24.33-61.62-25.32-67.11,1.13-3.44,1.99-6.98,2.58-10.55.92-5.82,3.02-19.11-1.38-29.2,2.28,2.19,4.33,4.6,6.29,7.03,1.18,1.46,2.4,2.87,3.68,4.21,10.16,11.56,22.09,22.06,32.62,31.32,13.18,11.59,24.56,21.61,25.39,27.78,0,.05.02.11.02.16.08.83.15,1.69.2,2.58,0,.06,0,.12,0,.19.18,6.35-1.32,14.88-3.02,24.53Z" fill="currentColor"></path><path d="M426.13,489.61c.6,7.11,1.65,16.86,2.91,27.28-9.43,1.68-20.01-1.22-26.23-7.31-5.11-5.01-16.47-20.29-11.65-53.86,2.36-16.44-.74-34.09-3.49-49.66-1.7-9.65-3.2-18.18-3.02-24.53,0-.07,0-.13,0-.19.05-.89.12-1.75.2-2.58,0-.05,0-.11.02-.16.84-6.17,12.22-16.19,25.39-27.78,10.5-9.23,22.37-19.69,32.5-31.19,1.33-1.39,2.59-2.83,3.81-4.34,1.96-2.42,4-4.83,6.27-7.02-4.4,10.09-2.29,23.37-1.36,29.19.57,3.57,1.44,7.12,2.58,10.57-1,5.53-6.13,29.33-25.33,67.12-9.62,19.52-6.96,39.65-4.38,59.11.7,5.32,1.37,10.34,1.79,15.35Z" fill="currentColor" stroke-width="0"></path></g><g id="hands" class="bodymap text-mw-gray active:text-mw-red-700 lg:hover:text-mw-red-100"><path d="M641.72,606.51c-.72-3.08-1.76-4.39-3.21-6.2-1.2-1.5-2.6-3.25-4-6.33.39,3.69,1.45,5.88,2.44,7.89,1.11,2.27,2.25,4.62,2.18,8.39-.07,3.27.15,5.48.38,7.62.33,3.14.62,6.1-.21,11.37-.3,1.91-.05,3.3.75,4.16.73.78,1.89,1.09,2.88,1.21,0-1.21.15-2.5.39-3.88,1.61-9.13,1.28-10.51.27-14.63-.51-2.08-1.2-4.91-1.87-9.61Z" fill="currentColor"></path><path d="M655.51,598.27c-.58-4.99-.72-7.9-.82-10.02q-.21-4.45-5.12-14.28l-.18-.36c-6.84-13.19-9.99-23.35-12.78-32.31-1.45-4.67-2.85-9.18-4.69-13.83-1.93,5.8-5.47,10.35-9.99,12.65-2.37,1.21-5.09,1.81-8.01,1.81-2.11,0-4.34-.31-6.58-.95.85,2.46,1.09,4.63.17,6.03-4.68,6.58-2.36,14.08.2,17.27,2.83,3.53,4.31,8.29,5.46,17.55.27,3.06.39,5.9.5,8.64.51,11.95,1.24,16.45,6.93,16.34,1.02-.03,1.84-.44,2.59-1.3,3.93-4.54,3.47-18.56,2.97-23.58-.06-.57.15-1.13.57-1.49.42-.38.99-.52,1.52-.39.19.05,4.63,1.19,7.36,6.91.04.09.08.18.1.27,1.84,6.32,3.76,8.72,5.45,10.85,1.55,1.93,3,3.77,3.91,7.68,0,.05.02.1.03.15.65,4.56,1.3,7.22,1.81,9.35,1.07,4.41,1.51,6.24-.22,16.09-.32,1.82-.42,3.43-.28,4.81.16,1.79.7,3.21,1.62,4.23,1.26,1.43,3.05,1.84,4.21,1.97,1.89-6.82,4.46-16.62,4.59-18.79l.02-.22c.21-3.33.56-8.92-1.34-25.07Z" fill="currentColor"></path><path d="M18.73,606.51c-.68,4.69-1.37,7.54-1.87,9.61-.99,4.13-1.33,5.52.27,14.63.24,1.38.38,2.68.38,3.88.99-.11,2.15-.43,2.88-1.21.8-.86,1.05-2.26.75-4.16-.84-5.27-.53-8.24-.21-11.37.22-2.14.44-4.35.38-7.62-.08-3.78,1.06-6.12,2.17-8.39.98-2.01,2.04-4.2,2.44-7.89-1.39,3.08-2.8,4.83-4,6.33-1.45,1.81-2.49,3.12-3.21,6.2Z" fill="currentColor" stroke-width="0"></path><path d="M52.99,547.07c-.92-1.42-.69-3.62.16-6.09-2.25.64-4.48.96-6.61.96-2.91,0-5.64-.6-8.01-1.81-4.53-2.3-8.08-6.87-9.99-12.67-1.83,4.66-3.23,9.18-4.69,13.85-2.79,8.96-5.95,19.12-12.8,32.34l-.16.33q-4.91,9.83-5.12,14.28c-.1,2.12-.23,5.03-.82,10.02-1.91,16.16-1.55,21.74-1.34,25.08l.02.21c.14,2.17,2.7,11.97,4.6,18.79,1.15-.12,2.94-.54,4.21-1.97.87-.98,1.4-2.33,1.59-4.01,0-.08,0-.15.02-.23.14-1.38.03-2.98-.28-4.8-1.74-9.85-1.29-11.68-.22-16.09.52-2.12,1.16-4.77,1.81-9.35,0-.05.02-.1.03-.15.91-3.91,2.37-5.74,3.91-7.68,1.69-2.12,3.61-4.53,5.45-10.85.03-.09.06-.18.1-.27,2.73-5.72,7.17-6.85,7.36-6.91.54-.13,1.1.02,1.52.39.42.37.63.93.57,1.49-.5,5.02-.96,19.04,2.97,23.58.75.87,1.57,1.28,2.6,1.3,5.73.12,6.43-4.39,6.92-16.34.12-2.74.24-5.58.5-8.57,1.16-9.34,2.64-14.09,5.47-17.62,2.56-3.19,4.88-10.69.23-17.22Z" fill="currentColor"></path></g><g id="forearms" class="bodymap text-mw-gray active:text-mw-red-700 lg:hover:text-mw-red-100"><path d="M629.69,522.22c-1.13,6.78-4.65,12.42-9.28,14.77-4.03,2.05-9.46,1.91-15.01-.38-2.21-4.21-5.19-8.37-6.61-10.26-.04-.04-.08-.1-.13-.14-8.02-8.58-14.46-13.59-20.69-18.45-8.62-6.72-16.75-13.06-28.61-28.19-12.92-16.47-16.63-24.46-20.56-32.91-2.42-5.19-4.92-10.57-9.56-18.2-.02-.03-.03-.05-.05-.08-2.17-3.18-6.32-8.99-11.24-15.81,7.32,5.22,14.13,7.31,19.91,7.31,3.54,0,6.69-.78,9.33-2.12,5.52-2.78,12.21-9.26,12.71-24.32.21-6.44-.72-13.94-2.78-22.46.3.72.6,1.44.91,2.18,2.49,6.15,5.69,9.56,11.5,15.77,4.14,4.41,9.81,10.46,17.68,20.13,13.13,16.15,26.59,50.24,37.42,77.64,4.49,11.37,8.74,22.12,12.4,30.19.02.04.03.08.05.11.97,1.78,1.83,3.52,2.62,5.24Z" fill="currentColor"></path><path d="M152.48,412.57c-4.91,6.81-9.05,12.62-11.22,15.8-.02.03-.03.05-.05.08-4.64,7.64-7.14,13.01-9.56,18.2-3.93,8.45-7.64,16.44-20.56,32.91-11.86,15.13-20,21.47-28.61,28.19-6.23,4.86-12.66,9.88-20.69,18.45-.04.04-.09.1-.13.14-1.41,1.89-4.39,6.04-6.6,10.26-5.54,2.29-10.98,2.44-15.02.38-4.63-2.35-8.15-7.98-9.28-14.77.79-1.71,1.65-3.45,2.62-5.24.02-.04.04-.08.06-.11,3.65-8.06,7.9-18.81,12.39-30.19,10.82-27.4,24.28-61.49,37.42-77.64,7.86-9.67,13.54-15.72,17.68-20.13,5.81-6.21,9.02-9.63,11.5-15.75.3-.71.58-1.41.88-2.1-2.04,8.48-2.96,15.95-2.75,22.36.49,15.06,7.18,21.54,12.7,24.32,2.64,1.34,5.79,2.12,9.33,2.12,5.77,0,12.57-2.09,19.88-7.29Z" fill="currentColor"></path></g><g class="bodymap text-mw-gray active:text-mw-red-700 lg:hover:text-mw-red-100" id="biceps"><path d="M535.68,414.61c-8.57,4.33-23.53,1.71-39.79-18.59-.02-.02-.03-.04-.05-.06-9.71-13.16-19.05-25.49-21.34-27.65-.5-.47-1.05-.99-1.66-1.54-4.5-4.14-11.31-10.39-15.31-20.4-.14-.35-.37-.62-.67-.8,0,0,0,0,0,0-.86-2.84-1.54-5.75-2.01-8.66-1.1-6.96-4.03-25.45,5.81-33.63,5.53-4.61,13.2-4.37,21.33-4.14.92.03,2.27.1,3.94.29,0,0,.03,0,.03,0,.04,0,.09.02.13.02.02,0,.03,0,.05,0,.03,0,.06,0,.09,0,2.54.31,5.82.88,9.54,1.98,0,0,0,0,.02,0,.14.06.29.11.45.12.02,0,.03,0,.03,0,2.49.75,5.18,1.73,7.97,3.01,3.26,1.5,6.39,3.29,9.32,5.29.45.54.96,1.08,1.5,1.67,2.81,3.02,7.61,8.17,16.01,24.38,0,.02.02.03.02.04,0,0,0,.02.02.03,25.45,57.59,13.79,73.98,4.57,78.62Z" fill="currentColor"></path><path d="M205.61,336.89c-.47,2.92-1.15,5.82-2.01,8.66-.3.19-.54.47-.68.82-4,10-10.81,16.26-15.31,20.4-.61.55-1.16,1.07-1.66,1.54-2.28,2.16-11.63,14.49-21.34,27.65-.02.02-.03.04-.05.06-16.26,20.3-31.21,22.91-39.79,18.59-9.21-4.64-20.86-21.02,4.53-78.53,0,0,0,0,0-.02l.03-.05c8.42-16.29,13.23-21.44,16.04-24.47.55-.59,1.05-1.13,1.51-1.68,2.92-2,6.05-3.78,9.3-5.27,2.82-1.29,5.55-2.28,8.08-3.03.11-.04.23-.07.35-.11,0,0,.02,0,.03,0,3.72-1.08,7.02-1.67,9.57-1.98.03,0,.07,0,.1,0h.02c.05,0,.11,0,.16-.02.02,0,.03,0,.04,0h0c1.67-.19,2.99-.26,3.91-.29,8.13-.24,15.8-.47,21.33,4.14,9.84,8.18,6.91,26.67,5.81,33.62Z" fill="currentColor"></path></g><g id="front-shoulders" class="bodymap text-mw-gray active:text-mw-red-700 lg:hover:text-mw-red-100"><path d="M510.6,303.91c-1.61-.91-3.25-1.75-4.94-2.53-7.64-3.49-14.56-4.88-19.19-5.42-7.44-1.87-13.18-4.12-16.62-6.5-12.81-8.87-22.9-20.97-27.79-26.83-4.59-5.52-7.95-10.07-10.92-14.08-6.46-8.74-11.12-15.05-22.12-22.2-5.2-3.37-11.11-5.94-15.09-7.49,8.34-2.74,26.96-7.7,44.59-4.11.97.24,1.99.51,3.04.78,1.25.32,2.34.6,3.08.8.34.09.84.17,1.59.3,32.54,5.37,45.89,28.26,51.36,46.48.75,2.74,1.56,4.76,2.58,7.33,2.02,5.07,5.06,12.69,10.42,33.46Z" fill="currentColor"></path><path d="M266.59,218.87c-3.98,1.55-9.89,4.12-15.09,7.49-10.99,7.14-15.66,13.45-22.12,22.2-2.97,4.01-6.33,8.57-10.96,14.13-4.84,5.81-14.94,17.91-27.76,26.79-3.42,2.39-9.16,4.62-16.62,6.5,0,0-.03,0-.03,0-2.75.32-6.31.94-10.34,2.14-.11.03-.21.06-.32.1-2.66.79-5.53,1.83-8.5,3.18-1.69.77-3.34,1.62-4.95,2.53,5.37-20.76,8.41-28.39,10.43-33.47,1.02-2.56,1.82-4.59,2.57-7.29,5.48-18.25,18.83-41.15,51.37-46.52.75-.12,1.25-.21,1.59-.3.74-.2,1.83-.48,3.09-.8,1.06-.27,2.08-.53,3.06-.79.02,0,.04-.02.07-.02,17.61-3.55,36.17,1.39,44.49,4.14Z" fill="currentColor"></path></g><g id="chest" class="bodymap text-mw-gray active:text-mw-red-700 lg:hover:text-mw-red-100"><path d="M473.89,295.55c-14.9.39-23.27,9.22-29.96,17.52-.93,1.15-1.89,2.26-2.88,3.34-.09.06-.16.13-.23.22-.15.17-.28.32-.44.49-18.16,19.25-45.55,26.23-69.71,17.75-1.63-.69-3.27-1.34-4.89-1.95-9.36-4.2-17.52-10.41-24.23-18.47-6.08-7.31-9.44-16.78-9.44-26.7v-49.81c0-.72.19-17.59,26.62-20.16,15.4-1.49,24.1,1.14,28.77,2.55.26.08.5.16.74.22.04.02.09.04.12.04,0,0,.03,0,.03,0h.02c.05.02.1.04.15.05.17.06.36.12.54.17,2.64.92,11.16,4,18.08,8.5,10.48,6.8,14.75,12.59,21.23,21.35,2.99,4.06,6.39,8.65,11.08,14.28,4.95,5.94,15.25,18.28,28.44,27.42,1.61,1.12,3.6,2.19,5.96,3.18Z" fill="currentColor"></path><path d="M328.4,237.95v49.79c0,9.9-3.35,19.39-9.44,26.7-6.71,8.06-14.87,14.27-24.23,18.47-1.62.61-3.25,1.26-4.87,1.94-24.1,8.48-51.46,1.55-69.61-17.62-.19-.2-.37-.41-.55-.61-.08-.09-.15-.16-.24-.22-.98-1.08-1.94-2.19-2.87-3.34-6.69-8.3-15.07-17.13-29.96-17.52,2.36-1,4.35-2.06,5.95-3.18,13.19-9.14,23.49-21.48,28.48-27.46,4.65-5.59,8.04-10.18,11.04-14.24,6.48-8.76,10.75-14.55,21.23-21.35,6.92-4.49,15.44-7.58,18.08-8.5.18-.05.36-.11.54-.17.05,0,.1-.03.15-.05.03,0,.07-.02.09-.03.03,0,.06,0,.09-.03h0c.23-.06.48-.14.73-.22,4.66-1.42,13.36-4.05,28.77-2.55,26.44,2.57,26.63,19.44,26.62,20.19Z" fill="currentColor" stroke-width="0"></path></g><g id="traps" class="bodymap text-mw-gray active:text-mw-red-700 lg:hover:text-mw-red-100"><path d="M287.92,178.7v20.85c0,10-5.2,13.67-16.13,17.47-3.35-1.33-19.62-7.35-37.88-7.02.31-.25.64-.52,1.01-.81,3.15-2.52,7.46-5.96,10.44-7.35,1.96-.92,6.52-2.88,11.35-4.94,7.4-3.17,15.8-6.78,18.88-8.35,3.68-1.87,9.2-6.86,12.32-9.84Z" fill="currentColor"></path><path d="M426.68,210c-18.26-.34-34.53,5.69-37.89,7.02-10.92-3.79-16.12-7.47-16.12-17.47v-20.84c3.12,2.97,8.63,7.96,12.31,9.83,3.08,1.57,11.48,5.18,18.88,8.35,4.83,2.06,9.39,4.02,11.35,4.94,2.98,1.39,7.29,4.83,10.44,7.35.37.3.71.57,1.02.81Z" fill="currentColor"></path></g><g id="body" class="body-map__model"><line x1="330.4" y1="504.57" x2="330.4" y2="571.92" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></line><line x1="330.17" y1="504.57" x2="330.17" y2="571.92" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></line><line x1="330.05" y1="504.57" x2="330.05" y2="571.92" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></line><path d="M29,521.95v.02c1.13,7.67,5.05,13.92,10.29,16.59,4.74,2.41,10.96,2.08,17.03-.58" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M145.75,308.53c2.76-1.9,6.01-3.82,9.75-5.55,11.35-5.19,20.97-5.55,22.92-5.6,8.44-.25,16.47-.47,22.46,4.53,10.6,8.82,7.57,27.94,6.41,35.27-.62,3.82-1.53,7.4-2.65,10.74" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M165.89,397.13c-18.42,22.99-34.28,22.87-41.87,19.05-12.72-6.41-19.87-27.43,3.8-80.98,0-.02,0-.05.02-.07" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M163.96,299.82h.04" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M174.18,297.7h.04c7.63-1.9,13.62-4.2,17.35-6.79,13.04-9.03,23.37-21.44,28.14-27.15,14.81-17.79,17.05-25.8,32.65-35.93,8.58-5.57,19.32-8.96,19.32-8.96,0,0-25.8-10.83-50.18-5.82h-.02" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M271.11,219.05c4.42-1.26,13.42-4.71,30.78-3.02,28.47,2.76,28.17,21.92,28.17,21.92v49.79c0,10.17-3.4,20.09-9.85,27.83-5.5,6.61-13.59,13.92-24.94,18.98-27.94,12.47-60.65,3.65-80.05-20.38-7.28-9.02-15.3-16.9-29.68-16.9" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M289.56,162.48v37.06c0,11.68-7.04,15.63-17.89,19.34-.54.18-1.1.36-1.67.52" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M330.05,340.19c0-16.6-24.92-9.38-34.79-5.64-10.73,4.06-19.35,8.64-22.18,15.17-3.41,7.86,3.69,12.72,4.53,33.36.22,5.38-.28,4.69-.19,15.43.09,11.2,2.29,60.48,4.34,93.33,3.22,51.71,34.57,94.43,48.29,94.43" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M286.4,377c14.08-4.19,23.65-6.71,37.6-6.43,6.05-.02,6.05-9.08,6.05-18.86" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M289.13,423.26c15.66.26,30.17.65,37.75-3.74,4.67-2.71,3.17-10.45,3.17-19.61" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M290.63,471.07c14.49.53,17.46,1.59,31.05,1.19,7.24-.55,8.38-3.7,8.38-19.3" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M298.53,379.04c4.89-1.29,14.76-4.25,22.72-2.87,7.97,1.39,8.8,6.57,8.8,12.58" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M298.53,428.4c6.43-.76,15.03-1.74,22.89-1.63,7.86.11,8.64,5.42,8.64,13.89" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><line x1="330.29" y1="504.57" x2="330.29" y2="571.92" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></line><path d="M298.53,477.34c5.89-.13,21.17,1.39,26.59,2.64,5.42,1.26,5.16,8.06,5.16,13.98" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M218.37,317.78c23.5,26.93,57.29,48.52,58.93,60.58,2.16,15.96-10.33,48.95-6.29,77.1,4.04,28.16-3.04,46.44-12.16,55.38-6.96,6.82-19.03,9.86-29.31,7.43" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M229.53,518.28c19.21,4.08,52.48,20.12,65.45,64.94,12.97,44.82,19.66,61.4,35.08,61.4" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M206.28,783.67c5.85,26.98,7.58,56.74,28.95,46.03,13.1-6.59,2.82,30.89,26.81,31.31,23.99.41,31.22-52.55,31.22-52.55" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M273.39,881.54c-.16.29-.32.56-.46.83-15.13,26.11-38.4,28.46-48.59,9.57-4.85-8.96-7.91-12.75-15.04-14.45" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><line x1="330.05" y1="504.57" x2="330.05" y2="571.92" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></line><line x1="330.29" y1="504.57" x2="330.29" y2="571.92" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></line><line x1="330.4" y1="504.57" x2="330.4" y2="571.92" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></line><path d="M631.46,521.95v.02c-1.13,7.67-5.05,13.92-10.29,16.59-4.74,2.41-10.96,2.08-17.03-.58" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M514.71,308.53c-2.76-1.9-6.01-3.82-9.75-5.55-11.35-5.19-20.97-5.55-22.92-5.6-8.44-.25-16.47-.47-22.46,4.53-10.6,8.82-7.57,27.94-6.41,35.27.62,3.82,1.53,7.4,2.65,10.74" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M494.57,397.13c18.42,22.99,34.28,22.87,41.87,19.05,12.72-6.41,19.87-27.43-3.8-80.98,0-.02,0-.05-.02-.07" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M496.5,299.82h-.04" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M486.27,297.7h-.04c-7.63-1.9-13.62-4.2-17.35-6.79-13.04-9.03-23.37-21.44-28.14-27.15-14.81-17.79-17.05-25.8-32.65-35.93-8.58-5.57-19.32-8.96-19.32-8.96,0,0,25.8-10.83,50.18-5.82h.02" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M389.35,219.05c-4.42-1.26-13.42-4.71-30.78-3.02-28.47,2.76-28.17,21.92-28.17,21.92v49.79c0,10.17,3.4,20.09,9.85,27.83,5.5,6.61,13.59,13.92,24.94,18.98,27.94,12.47,60.65,3.65,80.05-20.38,7.28-9.02,15.3-16.9,29.68-16.9" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M370.9,162.48v37.06c0,11.68,7.04,15.63,17.89,19.34.54.18,1.1.36,1.67.52" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M330.4,340.19c0-16.6,24.92-9.38,34.79-5.64,10.73,4.06,19.35,8.64,22.18,15.17,3.41,7.86-3.69,12.72-4.53,33.36-.22,5.38.28,4.69.19,15.43-.09,11.2-2.29,60.48-4.34,93.33-3.22,51.71-34.57,94.43-48.29,94.43" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M374.06,377c-14.08-4.19-23.65-6.71-37.6-6.43-6.05-.02-6.05-9.08-6.05-18.86" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M371.33,423.26c-15.66.26-30.17.65-37.75-3.74-4.67-2.71-3.17-10.45-3.17-19.61" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M369.83,471.07c-14.49.53-17.46,1.59-31.05,1.19-7.24-.55-8.38-3.7-8.38-19.3" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M361.93,379.04c-4.89-1.29-14.76-4.25-22.72-2.87-7.97,1.39-8.8,6.57-8.8,12.58" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M361.93,428.4c-6.43-.76-15.03-1.74-22.89-1.63-7.86.11-8.64,5.42-8.64,13.89" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><line x1="330.17" y1="504.57" x2="330.17" y2="571.92" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></line><path d="M361.93,477.34c-5.89-.13-21.17,1.39-26.59,2.64-5.42,1.26-5.16,8.06-5.16,13.98" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M442.09,317.78c-23.5,26.93-57.29,48.52-58.93,60.58-2.16,15.96,10.33,48.95,6.29,77.1-4.04,28.16,3.04,46.44,12.16,55.38,6.96,6.82,19.03,9.86,29.31,7.43" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M430.93,518.28c-19.21,4.08-52.48,20.12-65.45,64.94-12.97,44.82-19.66,61.4-35.08,61.4" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M454.18,783.67c-5.85,26.98-7.58,56.74-28.95,46.03-13.1-6.59-2.82,30.89-26.81,31.31-23.99.41-31.22-52.55-31.22-52.55" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M387.07,881.54c.16.29.32.56.46.83,15.13,26.11,38.4,28.46,48.59,9.57,4.85-8.96,7.91-12.75,15.04-14.45" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M282.69,83.67s-11.03-3.64-9.62,15.53c1.41,19.17,6.49,22.48,13.54,21.04,0,0,.56,9.8,3.39,15.57,0,0-.28,31.56-.99,39.35,0,0-9.17,9.22-14.25,11.82-5.08,2.59-25.25,10.95-30.19,13.26-4.94,2.31-12.98,9.66-14.53,10.38s-11.43,3.17-14.67,4.04c-3.24.86-40.77,3.32-54.17,48-2.26,8.22-5.08,9.95-13.26,41.8-2.82,9.22-9.31,1.01-37.1,68.03-4.09,10.09-10.16,12.4-28.92,35.46-18.76,23.06-37.53,80.63-50.04,108.21-9.99,18.48-9.43,31.75-22.35,56.69-7.46,14.93-4.33,8.55-6.29,25.23-1.96,16.68-1.54,22.28-1.33,25.62.21,3.33,5.03,20.44,5.03,20.44,0,0,10.99,1.03,8.51-13.09-2.49-14.12-.44-10.69,1.6-24.88,1.78-7.72,5.78-6.19,9.33-18.43,2.41-5.04,6.22-5.97,6.22-5.97,0,0-2.7,26.61,7.23,26.81,9.93.2,7.9-11.98,9.16-26.5,1.07-8.63,2.4-13.32,5.09-16.67,2.69-3.35,5.7-11.75.29-19.36-2.18-3.33,6.62-15.9,8.66-18.62,18.84-20.12,28.69-20.34,49.4-46.76,20.71-26.42,17.84-30.9,30.24-51.29,8.01-11.74,40.22-55.77,44.45-59.78,4.23-4.02,12.7-10.81,17.39-22.55,0,0,3.33,24.87,25.7,68.9,11.94,24.25,4.38,50.05,2.42,73.53-1.97,23.48-8.47,71.99-11.49,84.19-3.02,12.2-38.29,137.46-6.14,250.19-.32,27.05-2.52,26.73-3.78,33.82-1.26,7.09-.32,11.92-1.89,19.65-1.58,7.73-24.59,53.79-10.09,138.17,2.52,28.34,8.2,59.58,5.04,76.98-1.77,10.75-2.45,15.13-2.62,20.73-1.66,5.64-12.85,22.89-15.75,27.87-2.9,4.98-1.12,5.29-8.08,11.05-6.97,5.76-16.28,12.23-21.05,23.03-3.21,7.92-.39,11.35,3.6,11.43,0,0-.08,4.64,4.3,4.48,0,0,1.57,6.48,7.51,4.8,0,0,4.93,7.12,11.11,1.52,0,0,13.3,13.83,25.36-7.68,0,0,15.57-4.48,14.4-19.11-.16-4.72,4.54-7.2,11.11-11.03,6.57-3.84,10.8-6.8,6.97-29.43-.86-6.96.47-14.47.47-14.47,0,0,4.7-2.24,3.21-19.27-1.49-17.03-2.87-33.51,17.7-100.71,4.07-14.09,14.69-4.85,6.55-65.35-4.52-41.1-3.16-33.62,2.71-45.68,5.88-12.05,16.95-23.83,19.44-56.62,2.49-32.79,9.27-58.88,13.79-74.13,4.52-15.24,21.02-53.8,22.83-83.82.51-17.14.11-25.44,2.23-25.44" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M15.74,636.36s8.45,1.08,7.1-7.4c-1.35-8.48.36-10.41.19-18.76-.17-8.34,6.15-8.33,4.52-24.4" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M377.77,83.67s11.03-3.64,9.62,15.53-6.49,22.48-13.54,21.04c0,0-.56,9.8-3.39,15.57,0,0,.28,31.56.99,39.35,0,0,9.17,9.22,14.25,11.82,5.08,2.59,25.25,10.95,30.19,13.26,4.94,2.31,12.98,9.66,14.53,10.38s11.43,3.17,14.67,4.04c3.24.86,40.77,3.32,54.17,48,2.26,8.22,5.08,9.95,13.26,41.8,2.82,9.22,9.31,1.01,37.1,68.03,4.09,10.09,10.16,12.4,28.92,35.46,18.76,23.06,37.53,80.63,50.04,108.21,9.99,18.48,9.43,31.75,22.35,56.69,7.46,14.93,4.33,8.55,6.29,25.23,1.96,16.68,1.54,22.28,1.33,25.62-.21,3.33-5.03,20.44-5.03,20.44,0,0-10.99,1.03-8.51-13.09,2.49-14.12.44-10.69-1.6-24.88-1.78-7.72-5.78-6.19-9.33-18.43-2.41-5.04-6.22-5.97-6.22-5.97,0,0,2.7,26.61-7.23,26.81-9.93.2-7.9-11.98-9.16-26.5-1.07-8.63-2.4-13.32-5.09-16.67-2.69-3.35-5.7-11.75-.29-19.36,2.18-3.33-6.62-15.9-8.66-18.62-18.84-20.12-28.69-20.34-49.4-46.76-20.71-26.42-17.84-30.9-30.24-51.29-8.01-11.74-40.22-55.77-44.45-59.78s-12.7-10.81-17.39-22.55c0,0-3.33,24.87-25.7,68.9-11.94,24.25-4.38,50.05-2.42,73.53,1.97,23.48,8.47,71.99,11.49,84.19,3.02,12.2,38.29,137.46,6.14,250.19.32,27.05,2.52,26.73,3.78,33.82,1.26,7.09.32,11.92,1.89,19.65,1.58,7.73,24.59,53.79,10.09,138.17-2.52,28.34-8.2,59.58-5.04,76.98,1.77,10.75,2.45,15.13,2.62,20.73,1.66,5.64,12.85,22.89,15.75,27.87,2.9,4.98,1.12,5.29,8.08,11.05,6.97,5.76,16.28,12.23,21.05,23.03,3.21,7.92.39,11.35-3.6,11.43,0,0,.08,4.64-4.3,4.48,0,0-1.57,6.48-7.51,4.8,0,0-4.93,7.12-11.11,1.52,0,0-13.3,13.83-25.36-7.68,0,0-15.57-4.48-14.4-19.11.16-4.72-4.54-7.2-11.11-11.03-6.57-3.84-10.8-6.8-6.97-29.43.86-6.96-.47-14.47-.47-14.47,0,0-4.7-2.24-3.21-19.27,1.49-17.03,2.87-33.51-17.7-100.71-4.07-14.09-14.69-4.85-6.55-65.35,4.52-41.1,3.16-33.62-2.71-45.68-5.88-12.05-16.95-23.83-19.44-56.62-2.49-32.79-9.27-58.88-13.79-74.13-4.52-15.24-21.02-53.8-22.83-83.82-.51-17.14-.11-25.44-2.23-25.44" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M644.72,636.36s-8.45,1.08-7.1-7.4c1.35-8.48-.36-10.41-.19-18.76s-6.15-8.33-4.52-24.4" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M282.82,83.56s.54-14.9,0-24.54c-.54-9.63,6.51-27.77,16.85-22.43,10.89,5.63,32.28,10.76,58.33,0,7.35-2.26,14.26,10.09,15.52,24.09,1.25,14,2.69,21.23,4.3,22.88" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><g><path d="M370.84,135.77s-10.57,20.48-40.26,25.91" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path><path d="M289.97,135.77s10.57,20.48,40.26,25.91" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path></g><path d="M282.69,83.67c-5.36-3.86-3.61-20.07-6.05-37.39-2.56-18.19,16.27-32.16,24.89-34.59,5.86-1.65-18.28,1.29,6.39-6.76s33.37,2.13,37.66,3.41c3.74-.45-9.93-8.43,11.87-1.12,20.36,6.82,23.77,22.8,22.07,39.35-2.2,21.32,2.15,29.08-1.76,37.1" fill="none" stroke="#484a68" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></path></g><g id="shoulders" class="hidden"><ellipse id="hover-2" data-name="hover" cx="441.13" cy="261.13" rx="24.71" ry="25.24" fill="url(#jointradial)" opacity="1" stroke-width="0"></ellipse><ellipse id="hover" cx="219.33" cy="261.13" rx="24.71" ry="25.24" fill="url(#jointradial)" opacity="1" stroke-width="0"></ellipse><ellipse cx="441.13" cy="261.13" rx="12.04" ry="12.3" fill="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></ellipse><ellipse cx="219.33" cy="261.13" rx="12.04" ry="12.3" fill="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></ellipse></g><g id="elbow" class="hidden"><ellipse id="hover-3" data-name="hover" cx="118.6" cy="412.77" rx="24.71" ry="25.24" fill="url(#jointradial)" opacity="1" stroke-width="0"></ellipse><ellipse id="hover-7" data-name="hover" cx="541.86" cy="412.77" rx="24.71" ry="25.24" fill="url(#jointradial)" opacity="1" stroke-width="0"></ellipse><ellipse cx="541.86" cy="412.77" rx="12.04" ry="12.3" fill="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></ellipse><ellipse cx="118.6" cy="412.77" rx="12.04" ry="12.3" fill="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></ellipse></g><g id="wrist" class="hidden"><ellipse id="hover-4" data-name="hover" cx="42.41" cy="531.02" rx="24.71" ry="25.24" fill="url(#jointradial)" opacity="1" stroke-width="0"></ellipse><ellipse id="hover-8" data-name="hover" cx="618.05" cy="531.02" rx="24.71" ry="25.24" fill="url(#jointradial)" opacity="1" stroke-width="0"></ellipse><ellipse cx="618.05" cy="531.02" rx="12.04" ry="12.3" fill="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></ellipse><ellipse cx="42.41" cy="531.02" rx="12.04" ry="12.3" fill="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></ellipse></g><g id="hips" class="hidden"><ellipse id="hover-6" data-name="hover" cx="412.75" cy="523.37" rx="24.71" ry="25.24" fill="url(#jointradial)" opacity="1" stroke-width="0"></ellipse><ellipse id="hover-5" data-name="hover" cx="247.71" cy="523.37" rx="24.71" ry="25.24" fill="url(#jointradial)" opacity="1" stroke-width="0"></ellipse><ellipse cx="412.75" cy="523.37" rx="12.04" ry="12.3" fill="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></ellipse><ellipse cx="247.71" cy="523.37" rx="12.04" ry="12.3" fill="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></ellipse></g><g id="knees" class="hidden"><ellipse id="hover-10" data-name="hover" cx="419.37" cy="874.7" rx="24.71" ry="25.24" fill="url(#jointradial)" opacity="1" stroke-width="0"></ellipse><ellipse id="hover-9" data-name="hover" cx="241.09" cy="874.7" rx="24.71" ry="25.24" fill="url(#jointradial)" opacity="1" stroke-width="0"></ellipse><ellipse cx="419.37" cy="874.7" rx="12.04" ry="12.3" fill="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></ellipse><ellipse cx="241.09" cy="874.7" rx="12.04" ry="12.3" fill="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></ellipse></g><g id="ankles" class="hidden"><ellipse id="hover-11" data-name="hover" cx="221.14" cy="1105.65" rx="24.71" ry="25.24" fill="url(#jointradial)" opacity="1" stroke-width="0"></ellipse><ellipse id="hover-12" data-name="hover" cx="439.32" cy="1105.65" rx="24.71" ry="25.24" fill="url(#jointradial)" opacity="1" stroke-width="0"></ellipse><ellipse cx="439.32" cy="1105.65" rx="12.04" ry="12.3" fill="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></ellipse><ellipse cx="221.14" cy="1105.65" rx="12.04" ry="12.3" fill="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3.46"></ellipse></g></svg>`;


    return (
        <View style={styles.svgLoaderContainer}>
            <View style={styles.svgScanContainer}>
                <Animated.View
                    style={[
                        styles.svgWrapper,
                        {
                            transform: [{ scale: scaleValue }],
                            opacity: scanOpacity,
                        },
                    ]}
                >
                    <SvgXml xml={svgContent} width="200" height="320" />
                </Animated.View>

                {/* Scanning line effect */}
                <Animated.View
                    style={[
                        styles.scanLine,
                        {
                            transform: [{ translateY: scanLineY }],
                        },
                    ]}
                />
            </View>
            <Text style={styles.loadingText}>Scanning 3D Model...</Text>
            <Text style={styles.loadingSubtext}>Analyzing avatar data</Text>
            <Text style={styles.loadingNote}>Please wait while we prepare your model</Text>
        </View>
    );
}

// SVG Fallback for 3D Canvas - No 3D shapes, just return null to show main SVG loader
function SvgFallback3D() {
    // Return null so the main SVG loader outside Canvas handles everything
    return null;
}

// Error Boundary with better error handling
class ErrorBoundary extends React.Component<
    {children: React.ReactNode},
    {hasError: boolean, error: any, retryCount: number}
> {
    constructor(props: {children: React.ReactNode}) {
        super(props);
        this.state = { hasError: false, error: null, retryCount: 0 };
    }

    static getDerivedStateFromError(error: any) {
        return { hasError: true, error };
    }

    componentDidCatch(error: any, errorInfo: any) {
console.error('❌ 3D Model Error Boundary triggered:', error);
console.error('Error Info:', errorInfo);
    }

    retry = () => {
        if (this.state.retryCount < 2) {
            this.setState({
                hasError: false,
                error: null,
                retryCount: this.state.retryCount + 1
            });
        }
    };

    render() {
        if (this.state.hasError) {
            return (
                <View style={styles.errorContainer}>
                    <Text style={styles.errorText}>⚠️ 3D Model Issue</Text>
                    <Text style={styles.helpText}>
                        The model encountered a rendering issue.{'\n'}
                        {'\n'}
                        This can happen with complex models or textures.{'\n'}
                        {'\n'}
                        Retries: {this.state.retryCount}/2
                    </Text>
                    {this.state.retryCount < 2 && (
                        <Text
                            style={styles.retryButton}
                            onPress={this.retry}
                        >
                            🔄 Retry Loading
                        </Text>
                    )}
                </View>
            );
        }

        return this.props.children;
    }
}

// Color Combinations Panel Component
function ColorCombinationsPanel({
    colorCombinations,
    setColorCombinations,
    selectedCombination,
    setSelectedCombination
}: {
    colorCombinations: ColorCombination[];
    setColorCombinations: (combinations: ColorCombination[]) => void;
    selectedCombination: number;
    setSelectedCombination: (index: number) => void;
}) {
    const { user } = useAuth();
    const [colorRecommendations, setColorRecommendations] = useState<ColorRecommendation | null>(null);
    const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(false);
    const [colorSource, setColorSource] = useState<'gemini' | 'loading' | 'none'>('loading');

    // Load color recommendations when component mounts (always fresh data)
    useEffect(() => {
        // console.log('🎨 Male Model: Loading fresh recommendations on mount'); // REMOVED: Debug logging
        loadColorRecommendations();
    }, []);

    // Load fresh recommendations every time the page comes into focus
    const { useFocusEffect } = require('@react-navigation/native');
    useFocusEffect(
        React.useCallback(() => {
            // console.log('🎨 Male Model: Page focused - clearing state and loading fresh recommendations'); // REMOVED: Debug logging

            // Clear existing state to ensure fresh data
            setColorCombinations([]);
            setSelectedCombination(0);
            setColorSource('loading');
            setColorRecommendations(null);
            setIsLoadingRecommendations(true);

            // Load fresh recommendations
            loadColorRecommendations();
        }, [])
    );

    const loadColorRecommendations = async () => {
        setIsLoadingRecommendations(true);
        setColorSource('loading');

        try {
            // console.log('🎨 Male Model: Loading latest color recommendations from new endpoint...'); // REMOVED: Debug logging

            // Use the updated userRecommendationService to get latest recommendation
            const { userRecommendationService } = await import('../../services/userRecommendationService');
            const latestRecommendationData = await userRecommendationService.getLatestRecommendationData();

            if (latestRecommendationData && latestRecommendationData.recommendations && latestRecommendationData.recommendations.length > 0) {
                // console.log('✅ Male Model: Using latest recommendations from new endpoint'); // REMOVED: Debug logging

                setColorRecommendations(latestRecommendationData);

                // Convert recommendations to color combinations format
                const combinations = latestRecommendationData.recommendations.map((outfit: any, index: number) => ({
                    id: `latest-${index}`,
                    shirt: outfit.shirt?.hex || '#3498db',
                    pants: outfit.pants?.hex || '#2c3e50',
                    shoes: outfit.shoes?.hex || '#000000',
                    description: outfit.outfitName || `AI Style ${index + 1}`,
                    confidence: latestRecommendationData.confidence || 0.9,
                    source: 'gemini' as const,
                    name: outfit.outfitName || `Style ${index + 1}`,
                    reason: outfit.overallReason || 'AI recommended combination'
                }));

                setColorCombinations(combinations);
                setColorSource('gemini');
                // console.log('🎨 Male Model: Latest combinations loaded from new endpoint:', combinations); // REMOVED: Sensitive color combination data
                return;
            }

            // No recommendations available
            // console.log('🎨 Male Model: No recommendations available from latest endpoint'); // REMOVED: Debug logging
            setColorCombinations([]);
            setColorSource('none');

        } catch (error) {
            console.error('🎨 Male Model: Error loading recommendations from history:', error);
            setColorCombinations([]);
            setColorSource('none');
        } finally {
            setIsLoadingRecommendations(false);
        }
    };



    if (isLoadingRecommendations) {
        return (
            <View style={styles.combinationsPanel}>
                <View style={styles.combinationsHeader}>
                    <Ionicons name="sparkles" size={20} color="#e91e63" />
                    <Text style={styles.combinationsTitle}>Loading Current AI Recommendations...</Text>
                </View>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="small" color="#e91e63" />
                    <Text style={styles.loadingText}>Getting fresh Gemini AI recommendations...</Text>
                </View>
            </View>
        );
    }

    if (colorCombinations.length === 0) {
        return (
            <View style={styles.combinationsPanel}>
                <View style={styles.combinationsHeader}>
                    <Ionicons name="color-palette-outline" size={20} color="#9ca3af" />
                    <Text style={styles.combinationsTitle}>Current AI Recommendations</Text>
                </View>
                <View style={styles.noRecommendationsContainer}>
                    <Ionicons name="camera-outline" size={48} color="#9ca3af" />
                    <Text style={styles.noCombinationsText}>No current color recommendations available</Text>
                    <Text style={styles.noCombinationsSubtext}>Upload a photo for AI-powered color analysis</Text>
                </View>
            </View>
        );
    }

    return (
        <View style={styles.combinationsPanel}>
            <View style={styles.combinationsHeader}>
                <Ionicons name="sparkles" size={20} color="#e91e63" />
                <Text style={styles.combinationsTitle}>🤖 Current Gemini AI Recommendations</Text>
                <Text style={styles.combinationsCount}>({colorCombinations.length})</Text>
            </View>

            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.combinationsScroll}
                contentContainerStyle={styles.combinationsScrollContent}
            >
                {colorCombinations.map((combination, index) => (
                    <TouchableOpacity
                        key={combination.id}
                        style={[
                            styles.combinationCard,
                            selectedCombination === index && styles.selectedCombinationCard
                        ]}
                        onPress={() => setSelectedCombination(index)}
                    >
                        <Text style={styles.combinationName}>{combination.description}</Text>

                        {/* Color Preview */}
                        <View style={styles.colorPreview}>
                            <View style={[styles.colorBox, { backgroundColor: combination.shirt }]} />
                            <View style={[styles.colorBox, { backgroundColor: combination.pants }]} />
                            <View style={[styles.colorBox, { backgroundColor: combination.shoes }]} />
                        </View>

                        {/* Color Labels */}
                        <View style={styles.colorLabels}>
                            <Text style={styles.colorLabel}>Shirt</Text>
                            <Text style={styles.colorLabel}>Pants</Text>
                            <Text style={styles.colorLabel}>Shoes</Text>
                        </View>
                    </TouchableOpacity>
                ))}
            </ScrollView>
        </View>
    );
}

// Main viewer component with gesture controls
function Model3DViewer({ currentCombination }: { currentCombination: ColorCombination | null }) {
    const [isManualRotation, setIsManualRotation] = useState(false);
    const [isModelLoaded, setIsModelLoaded] = useState(false);
    const [isModelReady, setIsModelReady] = useState(false);
    const [rotationX, setRotationX] = useState(0);
    const [rotationY, setRotationY] = useState(0);

    // Touch gesture handling
    const lastTouchRef = useRef({ x: 0, y: 0 });
    const rotationSpeedRef = useRef(0.003); // Very slow rotation speed for mobile

    const panResponder = PanResponder.create({
        onMoveShouldSetPanResponder: () => true,
        onPanResponderGrant: (evt) => {
            setIsManualRotation(true);
            lastTouchRef.current = {
                x: evt.nativeEvent.pageX,
                y: evt.nativeEvent.pageY,
            };
        },
        onPanResponderMove: (evt) => {
            const deltaX = evt.nativeEvent.pageX - lastTouchRef.current.x;
            const deltaY = evt.nativeEvent.pageY - lastTouchRef.current.y;

            // Apply slow, smooth rotation
            setRotationY(prev => prev + deltaX * rotationSpeedRef.current);
            setRotationX(prev => {
                const newRotation = prev - deltaY * rotationSpeedRef.current;
                // Limit vertical rotation to prevent flipping
                return Math.max(-Math.PI / 3, Math.min(Math.PI / 3, newRotation));
            });

            lastTouchRef.current = {
                x: evt.nativeEvent.pageX,
                y: evt.nativeEvent.pageY,
            };
        },
        onPanResponderRelease: () => {
            // Keep manual rotation - no auto-rotation
            setIsManualRotation(true);
        },
    });

    // Smart model loading - only hide SVG when model is actually ready
    useEffect(() => {
        let modelCheckTimer: ReturnType<typeof setTimeout> | undefined;
        let readyCheckTimer: ReturnType<typeof setTimeout> | undefined;

        // First timer: Start checking for model after 5-6 seconds
        const startModelCheck = () => {
            modelCheckTimer = setTimeout(() => {
                // console.log('🔍 Starting to check if model is ready...'); // REMOVED: Debug logging
                setIsModelLoaded(true); // This allows the 3D canvas to start

                // Second timer: Check if model is actually rendered and ready
                readyCheckTimer = setTimeout(() => {
                    // console.log('✅ Model should be ready now'); // REMOVED: Debug logging
                    setIsModelReady(true); // This hides the SVG
                }, 2000); // Give 2 more seconds for model to fully load

            }, 5500); // 5.5 seconds initial wait
        };

        startModelCheck();

        return () => {
            if (modelCheckTimer) clearTimeout(modelCheckTimer);
            if (readyCheckTimer) clearTimeout(readyCheckTimer);
        };
    }, []);

    return (
        <View style={styles.fullModelContainer}>
            {/* Show SVG animated loader until model is actually ready */}
            {!isModelReady && (
                <View style={styles.loaderOverlay}>
                    <SvgLoader />
                </View>
            )}

            <View style={[styles.canvas, !isModelLoaded && styles.hiddenCanvas]} {...panResponder.panHandlers}>
                <ErrorBoundary>
                    <Canvas
                        style={styles.canvasInner}
                        camera={{ position: [0, 1.5, 3.5], fov: 75 }}
                        gl={{
                            antialias: true,
                            alpha: true,
                            powerPreference: "high-performance",
                        }}
                    >
                        {/* Light gray background for better model visibility */}
                        <color attach="background" args={['#f1f5f9']} />

                        {/* Enhanced lighting for better model visibility on light background */}
                        <ambientLight intensity={0.6} />
                        <directionalLight position={[10, 10, 5]} intensity={1.2} color="#ffffff" />
                        <directionalLight position={[-10, 5, -5]} intensity={0.8} color="#3b82f6" />
                        <pointLight position={[0, 5, 0]} intensity={0.5} color="#fbbf24" />
                        <pointLight position={[0, -2, 2]} intensity={0.4} color="#ffffff" />

                        <Suspense fallback={<SvgFallback3D />}>
                            <Model3D
                                rotationX={rotationX}
                                rotationY={rotationY}
                                isManualRotation={isManualRotation}
                                colorCombination={currentCombination || undefined}
                            />
                        </Suspense>
                    </Canvas>
                </ErrorBoundary>
            </View>

            {isModelReady && (
                <View style={styles.statusContainer}>
                    <Text style={styles.statusText}>

                    </Text>
                </View>
            )}
        </View>
    );
}

export default function TabTwoScreen() {
    const { user } = useAuth();

    // Shared state for color combinations
    const [colorCombinations, setColorCombinations] = useState<ColorCombination[]>([]);
    const [selectedCombination, setSelectedCombination] = useState(0);
    const [currentCombination, setCurrentCombination] = useState<ColorCombination | null>(null);

    // Update current combination when selection changes
    useEffect(() => {
        if (colorCombinations.length > 0 && selectedCombination < colorCombinations.length) {
            setCurrentCombination(colorCombinations[selectedCombination]);
        }
    }, [colorCombinations, selectedCombination]);

    // Only allow male users to access this screen
    if (user?.gender !== 'male') {
        return (
            <View style={styles.accessDeniedContainer}>
                <Ionicons name="lock-closed" size={60} color="#e74c3c" />
                <Text style={styles.accessDeniedTitle}>Access Restricted</Text>
                <Text style={styles.accessDeniedText}>
                    This 3D model is only available for male users.
                </Text>
                <Text style={styles.accessDeniedSubtext}>
                    Your profile shows: {user?.gender || 'Unknown gender'}
                </Text>
            </View>
        );
    }

    return (
        <View style={styles.fullScreenContainer}>
            <View style={styles.modelContainer}>
                <Model3DViewer currentCombination={currentCombination} />
            </View>
            <ColorCombinationsPanel
                colorCombinations={colorCombinations}
                setColorCombinations={setColorCombinations}
                selectedCombination={selectedCombination}
                setSelectedCombination={setSelectedCombination}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    fullScreenContainer: {
        flex: 1,
        backgroundColor: '#f1f5f9', // Light gray background
    },
    fullModelContainer: {
        flex: 1,
        position: 'relative',
        backgroundColor: '#f1f5f9', // Light gray background
    },
    canvas: {
        flex: 1,
    },
    canvasInner: {
        flex: 1,
    },
    hiddenCanvas: {
        opacity: 0,
    },
    loaderOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f1f5f9',
        zIndex: 1000,
    },
    svgLoaderContainer: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    svgScanContainer: {
        position: 'relative',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 30,
    },
    svgWrapper: {
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#6366f1',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    scanLine: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: 3,
        backgroundColor: '#6366f1',
        shadowColor: '#6366f1',
        shadowOffset: {
            width: 0,
            height: 0,
        },
        shadowOpacity: 0.8,
        shadowRadius: 10,
        elevation: 5,
    },
    loadingText: {
        fontSize: 16,
        color: '#374151',
        fontWeight: '600',
        textAlign: 'center',
        marginBottom: 8,
    },
    loadingSubtext: {
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'center',
        marginBottom: 8,
    },
    loadingNote: {
        fontSize: 12,
        color: '#9ca3af',
        textAlign: 'center',
        fontStyle: 'italic',
    },

    errorContainer: {
        position: 'absolute',
        top: '50%',
        left: 20,
        right: 20,
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        padding: 20,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#ef4444',
        transform: [{ translateY: -75 }],
    },
    errorText: {
        color: '#ef4444',
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 10,
    },
    helpText: {
        color: '#d1d5db',
        fontSize: 14,
        textAlign: 'center',
        lineHeight: 20,
        marginBottom: 15,
    },
    retryButton: {
        color: '#3b82f6',
        fontSize: 16,
        fontWeight: 'bold',
        textAlign: 'center',
        padding: 10,
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#3b82f6',
    },
    accessDeniedContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f8f9fa',
        padding: 40,
    },
    accessDeniedTitle: {
        fontSize: 24,
        fontWeight: '700',
        color: '#e74c3c',
        marginTop: 20,
        marginBottom: 16,
        textAlign: 'center',
    },
    accessDeniedText: {
        fontSize: 16,
        color: '#2c3e50',
        textAlign: 'center',
        lineHeight: 24,
        marginBottom: 12,
    },
    accessDeniedSubtext: {
        fontSize: 14,
        color: '#7f8c8d',
        textAlign: 'center',
        fontStyle: 'italic',
    },
    // Color Combinations Panel Styles
    modelContainer: {
        flex: 1,
    },
    combinationsPanel: {
        backgroundColor: '#ffffff',
        borderTopWidth: 1,
        borderTopColor: '#e2e8f0',
        paddingVertical: 16,
        paddingHorizontal: 16,
        maxHeight: 200,
    },
    combinationsHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    combinationsTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#1f2937',
        marginLeft: 8,
    },
    combinationsCount: {
        fontSize: 14,
        color: '#6b7280',
        marginLeft: 4,
    },
    combinationsScroll: {
        flexGrow: 0,
    },
    combinationsScrollContent: {
        paddingRight: 16,
    },
    combinationCard: {
        backgroundColor: '#f8fafc',
        borderRadius: 12,
        padding: 12,
        marginRight: 12,
        minWidth: 120,
        borderWidth: 2,
        borderColor: 'transparent',
    },
    selectedCombinationCard: {
        backgroundColor: '#eff6ff',
        borderColor: '#3b82f6',
    },
    combinationName: {
        fontSize: 12,
        fontWeight: '600',
        color: '#374151',
        textAlign: 'center',
        marginBottom: 8,
    },
    colorPreview: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 6,
    },
    colorBox: {
        width: 24,
        height: 24,
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#e5e7eb',
    },
    colorLabels: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    colorLabel: {
        fontSize: 10,
        color: '#6b7280',
        textAlign: 'center',
        flex: 1,
    },
    loadingContainer: {
        alignItems: 'center',
        paddingVertical: 20,
    },
    noCombinationsText: {
        fontSize: 14,
        color: '#6b7280',
        textAlign: 'center',
        paddingVertical: 20,
    },
    statusContainer: {
        position: 'absolute',
        bottom: 20,
        left: 20,
        right: 20,
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        borderRadius: 8,
        padding: 10,
    },
    statusText: {
        color: '#ffffff',
        fontSize: 12,
        textAlign: 'center',
    },
    noRecommendationsContainer: {
        alignItems: 'center',
        paddingVertical: 32,
        paddingHorizontal: 20,
    },
    noCombinationsSubtext: {
        fontSize: 12,
        color: '#9ca3af',
        textAlign: 'center',
        marginTop: 8,
        fontStyle: 'italic',
    },
});