import React, { useState } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { NativeAd, NativeAdView, HeadlineView, TaglineView, AdvertiserView, CallToActionView, IconView } from 'react-native-google-mobile-ads';
import { getAdUnitIdForEnvironment, ADMOB_CONFIG } from '../../services/admobService';

interface NativeAdComponentProps {
  adLocation: 'matchWithApp1' | 'matchWithApp2';
  style?: any;
  onAdLoaded?: () => void;
  onAdFailedToLoad?: (error: any) => void;
}

const NativeAdComponent: React.FC<NativeAdComponentProps> = ({
  adLocation,
  style,
  onAdLoaded,
  onAdFailedToLoad,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get the appropriate ad unit ID for the location
  const adConfig = ADMOB_CONFIG.NATIVE_ADS[adLocation];
  const adUnitId = getAdUnitIdForEnvironment(adConfig, 'banner'); // Using banner as fallback for test

  const handleAdLoaded = () => {
    setIsLoaded(true);
    setError(null);
    onAdLoaded?.();
  };

  const handleAdFailedToLoad = (error: any) => {
    setIsLoaded(false);
    setError(error.message || 'Failed to load native ad');
    onAdFailedToLoad?.(error);
    console.warn(`Native ad failed to load for ${adLocation}:`, error);
  };

  const handleAdOpened = () => {
    console.log(`Native ad opened for ${adLocation}`);
  };

  const handleAdClosed = () => {
    console.log(`Native ad closed for ${adLocation}`);
  };

  if (!adUnitId) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <NativeAd
        unitId={adUnitId}
        onAdLoaded={handleAdLoaded}
        onAdFailedToLoad={handleAdFailedToLoad}
        onAdOpened={handleAdOpened}
        onAdClosed={handleAdClosed}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false,
        }}
      >
        <NativeAdView style={styles.nativeAdView}>
          <View style={styles.adHeader}>
            <IconView style={styles.adIcon} />
            <View style={styles.adHeaderText}>
              <HeadlineView style={styles.adHeadline} />
              <AdvertiserView style={styles.adAdvertiser} />
            </View>
          </View>
          
          <TaglineView style={styles.adTagline} />
          
          <View style={styles.adFooter}>
            <CallToActionView style={styles.adCallToAction} />
          </View>
        </NativeAdView>
      </NativeAd>
      
      {/* Optional: Add loading indicator or error message */}
      {error && __DEV__ && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Native Ad Error: {error}</Text>
        </View>
      )}
    </View>
  );
};

// Compact Native Ad for smaller spaces
export const CompactNativeAd: React.FC<Omit<NativeAdComponentProps, 'style'>> = (props) => {
  return (
    <NativeAdComponent 
      {...props} 
      style={styles.compactContainer}
    />
  );
};

// Large Native Ad for prominent placements
export const LargeNativeAd: React.FC<Omit<NativeAdComponentProps, 'style'>> = (props) => {
  return (
    <NativeAdComponent 
      {...props} 
      style={styles.largeContainer}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  compactContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 8,
    marginVertical: 4,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  largeContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginVertical: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 8,
  },
  nativeAdView: {
    flex: 1,
  },
  adHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  adIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 12,
  },
  adHeaderText: {
    flex: 1,
  },
  adHeadline: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 2,
  },
  adAdvertiser: {
    fontSize: 12,
    color: '#64748b',
  },
  adTagline: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    marginBottom: 12,
  },
  adFooter: {
    alignItems: 'flex-end',
  },
  adCallToAction: {
    backgroundColor: '#2563eb',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 80,
    alignItems: 'center',
  },
  errorContainer: {
    padding: 8,
    backgroundColor: '#ffebee',
    borderRadius: 4,
    marginTop: 4,
  },
  errorText: {
    color: '#c62828',
    fontSize: 12,
    textAlign: 'center',
  },
});

export default NativeAdComponent;
