import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    Dimensions,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { useAuth } from '../../contexts/AuthContext';

// Import components that we'll create
import BannerAdComponent from '../../components/ads/BannerAd';
import { AnalysisHistory } from '../../components/settings/AnalysisHistory';
import { ColorHistory } from '../../components/settings/ColorHistory';
import { Credits } from '../../components/settings/Credits';
import { MeasurementsHistory } from '../../components/settings/MeasurementsHistory';
import { PrivacyPolicy } from '../../components/settings/PrivacyPolicy';
import { TermsAndConditions } from '../../components/settings/TermsAndConditions';
import { UserProfile } from '../../components/settings/UserProfile';

const { width } = Dimensions.get('window');

type SettingsPage =
  | 'main'
  | 'terms'
  | 'privacy'
  | 'credits'
  | 'analysis-history'
  | 'color-history'
  | 'measurements-history'
  | 'profile';

export default function SettingsScreen() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState<SettingsPage>('main');

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              router.replace('/');
            } catch (error: any) {
              Alert.alert('Error', 'Failed to logout: ' + error.message);
            }
          },
        },
      ]
    );
  };

  const renderMainSettings = () => (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.iconContainer}>
            <Ionicons name="settings" size={40} color="#2563eb" />
          </View>
          <Text style={styles.headerTitle}>Settings</Text>
          <Text style={styles.headerSubtitle}>Manage your account and preferences</Text>
        </View>
      </View>

      <View style={styles.settingsContainer}>
        {/* User Profile Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => setCurrentPage('profile')}
          >
            <View style={styles.settingItemLeft}>
              <Ionicons name="person" size={24} color="#2563eb" />
              <View style={styles.settingItemText}>
                <Text style={styles.settingItemTitle}>Profile</Text>
                <Text style={styles.settingItemSubtitle}>Edit your profile and change password</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#64748b" />
          </TouchableOpacity>
        </View>

        {/* History Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>History</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => setCurrentPage('analysis-history')}
          >
            <View style={styles.settingItemLeft}>
              <Ionicons name="analytics" size={24} color="#2563eb" />
              <View style={styles.settingItemText}>
                <Text style={styles.settingItemTitle}>Analysis History</Text>
                <Text style={styles.settingItemSubtitle}>View your face analysis history</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#64748b" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => setCurrentPage('color-history')}
          >
            <View style={styles.settingItemLeft}>
              <Ionicons name="color-palette" size={24} color="#2563eb" />
              <View style={styles.settingItemText}>
                <Text style={styles.settingItemTitle}>Color History</Text>
                <Text style={styles.settingItemSubtitle}>View your color recommendations</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#64748b" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => setCurrentPage('measurements-history')}
          >
            <View style={styles.settingItemLeft}>
              <Ionicons name="resize" size={24} color="#2563eb" />
              <View style={styles.settingItemText}>
                <Text style={styles.settingItemTitle}>Measurements History</Text>
                <Text style={styles.settingItemSubtitle}>View your face measurements</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#64748b" />
          </TouchableOpacity>
        </View>



        {/* Legal Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Legal</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => setCurrentPage('terms')}
          >
            <View style={styles.settingItemLeft}>
              <Ionicons name="document-text" size={24} color="#2563eb" />
              <View style={styles.settingItemText}>
                <Text style={styles.settingItemTitle}>Terms & Conditions</Text>
                <Text style={styles.settingItemSubtitle}>Read our terms of service</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#64748b" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => setCurrentPage('privacy')}
          >
            <View style={styles.settingItemLeft}>
              <Ionicons name="shield-checkmark" size={24} color="#2563eb" />
              <View style={styles.settingItemText}>
                <Text style={styles.settingItemTitle}>Privacy Policy</Text>
                <Text style={styles.settingItemSubtitle}>Read our privacy policy</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#64748b" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => setCurrentPage('credits')}
          >
            <View style={styles.settingItemLeft}>
              <Ionicons name="people" size={24} color="#2563eb" />
              <View style={styles.settingItemText}>
                <Text style={styles.settingItemTitle}>Credits & Attributions</Text>
                <Text style={styles.settingItemSubtitle}>3D model creators and contributors</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#64748b" />
          </TouchableOpacity>
        </View>

        {/* Logout Section */}
        <View style={styles.section}>
          <TouchableOpacity
            style={[styles.settingItem, styles.logoutItem]}
            onPress={handleLogout}
          >
            <View style={styles.settingItemLeft}>
              <Ionicons name="log-out" size={24} color="#dc2626" />
              <View style={styles.settingItemText}>
                <Text style={[styles.settingItemTitle, styles.logoutText]}>Logout</Text>
                <Text style={styles.settingItemSubtitle}>Sign out of your account</Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>

        {/* Banner Ad */}
        <BannerAdComponent
          adLocation="settings"
          style={{ marginTop: 20, marginBottom: 20 }}
        />
      </View>
    </ScrollView>
  );

  const renderBackButton = () => (
    <TouchableOpacity
      style={styles.backButton}
      onPress={() => setCurrentPage('main')}
    >
      <Ionicons name="arrow-back" size={24} color="#667eea" />
      <Text style={styles.backButtonText}>Back</Text>
    </TouchableOpacity>
  );

  // Render different pages based on current selection
  switch (currentPage) {
    case 'terms':
      return (
        <View style={styles.pageContainer}>
          {renderBackButton()}
          <TermsAndConditions />
        </View>
      );
    case 'privacy':
      return (
        <View style={styles.pageContainer}>
          {renderBackButton()}
          <PrivacyPolicy />
        </View>
      );
    case 'credits':
      return (
        <View style={styles.pageContainer}>
          {renderBackButton()}
          <Credits />
        </View>
      );
    case 'analysis-history':
      return (
        <View style={styles.pageContainer}>
          {renderBackButton()}
          <AnalysisHistory />
        </View>
      );
    case 'color-history':
      return (
        <View style={styles.pageContainer}>
          {renderBackButton()}
          <ColorHistory />
        </View>
      );
    case 'measurements-history':
      return (
        <View style={styles.pageContainer}>
          {renderBackButton()}
          <MeasurementsHistory />
        </View>
      );
    case 'profile':
      return (
        <View style={styles.pageContainer}>
          {renderBackButton()}
          <UserProfile />
        </View>
      );
    default:
      return renderMainSettings();
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  contentContainer: {
    paddingBottom: 100,
  },
  header: {
    backgroundColor: '#f8fafc',
    paddingTop: 60,
    paddingBottom: 32,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerContent: {
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f1f5f9',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
  },
  settingsContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 16,
    marginLeft: 4,
  },
  settingItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingItemText: {
    marginLeft: 16,
    flex: 1,
  },
  settingItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 4,
  },
  settingItemSubtitle: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
  logoutItem: {
    borderColor: '#dc2626',
    borderWidth: 1,
  },
  logoutText: {
    color: '#dc2626',
  },
  disabledItem: {
    opacity: 0.5,
  },
  pageContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    paddingTop: 60,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  backButtonText: {
    fontSize: 16,
    color: '#2563eb',
    marginLeft: 8,
    fontWeight: '600',
  },
});
