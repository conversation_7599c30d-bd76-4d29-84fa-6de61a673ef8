// Authentication Flow Debug Script
// Run this in the browser console or as a Node.js script to test the API endpoints

const BASE_URL = 'https://faceapp-ttwh.onrender.com/api';

// Test user data
const testUser = {
  name: 'Test User',
  email: '<EMAIL>',
  password: 'testpass123',
  gender: 'male'
};

// Helper function to make API requests
async function makeRequest(endpoint, options = {}) {
  const config = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    // console.log(`🔄 Making request to: ${BASE_URL}${endpoint}`); // REMOVED: Sensitive API endpoint
    // console.log('📤 Request config:', {
    //   method: config.method,
    //   headers: config.headers,
    //   hasBody: !!config.body
    // }); // REMOVED: Sensitive request headers

    const response = await fetch(`${BASE_URL}${endpoint}`, config);
    const data = await response.json();

    // console.log(`📥 Response (${response.status}):`, {
    //   ok: response.ok,
    //   status: response.status,
    //   data: endpoint.includes('/auth/') ? { ...data, token: data.token ? '[HIDDEN]' : undefined } : data
    // }); // REMOVED: Sensitive API response data

    return { response, data };
  } catch (error) {
    console.error('❌ Request failed:', error);
    throw error;
  }
}

// Test health check
async function testHealthCheck() {
  console.log('\n🏥 Testing Health Check...');
  try {
    const { response, data } = await makeRequest('/health');
    if (response.ok) {
      console.log('✅ Health check passed');
      return true;
    } else {
      console.log('❌ Health check failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Health check error:', error.message);
    return false;
  }
}

// Test registration
async function testRegistration() {
  console.log('\n📝 Testing Registration...');
  try {
    const { response, data } = await makeRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(testUser),
    });

    if (response.ok && data.success) {
      console.log('✅ Registration successful');
      return data.token;
    } else {
      console.log('❌ Registration failed:', data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ Registration error:', error.message);
    return null;
  }
}

// Test login
async function testLogin() {
  console.log('\n🔐 Testing Login...');
  try {
    const { response, data } = await makeRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password,
      }),
    });

    if (response.ok && data.success) {
      console.log('✅ Login successful');
      return data.token;
    } else {
      console.log('❌ Login failed:', data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return null;
  }
}

// Test profile access
async function testProfile(token) {
  console.log('\n👤 Testing Profile Access...');
  try {
    const { response, data } = await makeRequest('/auth/me', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (response.ok) {
      console.log('✅ Profile access successful');
      console.log('👤 User data:', {
        id: data.id,
        name: data.name,
        email: data.email,
        gender: data.gender
      });
      return data;
    } else {
      console.log('❌ Profile access failed:', data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ Profile access error:', error.message);
    return null;
  }
}

// Run complete test suite
async function runTests() {
  console.log('🚀 Starting Authentication Flow Tests...');
  console.log('=' .repeat(50));

  // Test 1: Health Check
  const healthOk = await testHealthCheck();
  if (!healthOk) {
    console.log('🛑 Stopping tests - API is not healthy');
    return;
  }

  // Test 2: Registration
  let token = await testRegistration();
  if (token) {
    // Test 3: Profile access with registration token
    await testProfile(token);
  }

  // Test 4: Login (even if registration failed)
  token = await testLogin();
  if (token) {
    // Test 5: Profile access with login token
    await testProfile(token);
  }

  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Tests completed!');
}

// Export for use in React Native or run directly
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runTests, testHealthCheck, testRegistration, testLogin, testProfile };
} else {
  // Run tests if in browser
  runTests();
}
