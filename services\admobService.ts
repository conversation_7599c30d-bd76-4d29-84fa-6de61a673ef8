import { Platform } from 'react-native';
import mobileAds, {
  MaxAdContentRating,
} from 'react-native-google-mobile-ads';

// AdMob Configuration
export const ADMOB_CONFIG = {
  // Your actual AdMob App ID
  APP_ID: {
    android: 'ca-app-pub-4977543488988533~1311624736',
  },

  // Banner Ad Unit IDs (using your single banner ad unit for all locations)
  BANNER_ADS: {
    home: {
      android: 'ca-app-pub-4977543488988533/9925128841',
      ios: 'ca-app-pub-3940256099942544/2934735716', // Test ID for iOS
    },
    explore: {
      android: 'ca-app-pub-4977543488988533/9925128841',
      ios: 'ca-app-pub-3940256099942544/2934735716', // Test ID for iOS
    },
    female: {
      android: 'ca-app-pub-4977543488988533/9925128841',
      ios: 'ca-app-pub-3940256099942544/2934735716', // Test ID for iOS
    },
    settings: {
      android: 'ca-app-pub-4977543488988533/9925128841',
      ios: 'ca-app-pub-3940256099942544/2934735716', // Test ID for iOS
    },
  },

  // Interstitial Ad Unit IDs
  INTERSTITIAL_ADS: {
    screenTransition: {
      android: 'ca-app-pub-4977543488988533/4624183785',
      ios: 'ca-app-pub-3940256099942544/4411468910', // Test ID for iOS
    },
    modelView: {
      android: 'ca-app-pub-4977543488988533/4624183785',
      ios: 'ca-app-pub-3940256099942544/4411468910', // Test ID for iOS
    },
  },

  // Native Ad Unit IDs (if you want to add them later)
  NATIVE_ADS: {
    matchWithApp1: {
      android: 'ca-app-pub-3940256099942544/2247696110', // Test ID
      ios: 'ca-app-pub-3940256099942544/3986624511', // Test ID
    },
    matchWithApp2: {
      android: 'ca-app-pub-3940256099942544/2247696110', // Test ID
      ios: 'ca-app-pub-3940256099942544/3986624511', // Test ID
    },
  },

  // Rewarded Ad Unit IDs (if you want to add them later)
  REWARDED_ADS: {
    extraFeatures: {
      android: 'ca-app-pub-3940256099942544/5224354917', // Test ID
      ios: 'ca-app-pub-3940256099942544/1712485313', // Test ID
    },
  },
};

// Helper function to get the correct ad unit ID based on platform and environment
export const getAdUnitIdForEnvironment = (
  adConfig: { android: string; ios?: string },
  adType: 'banner' | 'interstitial' | 'native' | 'rewarded'
): string => {
  const platform = Platform.OS;
  
  // In development, use test ad unit IDs
  if (__DEV__) {
    const testAdUnits = {
      banner: {
        android: 'ca-app-pub-3940256099942544/6300978111',
        ios: 'ca-app-pub-3940256099942544/2934735716',
      },
      interstitial: {
        android: 'ca-app-pub-3940256099942544/1033173712',
        ios: 'ca-app-pub-3940256099942544/4411468910',
      },
      native: {
        android: 'ca-app-pub-3940256099942544/2247696110',
        ios: 'ca-app-pub-3940256099942544/3986624511',
      },
      rewarded: {
        android: 'ca-app-pub-3940256099942544/5224354917',
        ios: 'ca-app-pub-3940256099942544/1712485313',
      },
    };
    
    return testAdUnits[adType][platform] || testAdUnits[adType].android;
  }
  
  // In production, use your actual ad unit IDs
  return adConfig[platform] || adConfig.android;
};

// AdMob initialization and configuration
export const initializeAdMob = async () => {
  try {
    // Initialize AdMob
    await mobileAds().initialize();
    
    // Configure AdMob request settings
    await mobileAds().setRequestConfiguration({
      // Set maximum ad content rating
      maxAdContentRating: MaxAdContentRating.PG,
      
      // Set tag for child-directed treatment (false = not child-directed)
      tagForChildDirectedTreatment: false,
      
      // Set tag for users under age of consent (false = not under age)
      tagForUnderAgeOfConsent: false,
      
      // Add test device IDs for development
      testDeviceIdentifiers: [
        // Add your test device IDs here
        // 'YOUR_TEST_DEVICE_ID_1',
        // 'YOUR_TEST_DEVICE_ID_2',
      ],
    });
    
    console.log('AdMob initialized successfully');
  } catch (error) {
    console.error('Failed to initialize AdMob:', error);
  }
};

// Frequency control for interstitial ads
let lastInterstitialShown = 0;
const INTERSTITIAL_FREQUENCY_MS = 60000; // 1 minute

export const canShowInterstitial = (): boolean => {
  const now = Date.now();
  return now - lastInterstitialShown >= INTERSTITIAL_FREQUENCY_MS;
};

export const markInterstitialShown = (): void => {
  lastInterstitialShown = Date.now();
};

// Helper function to check if AdMob is properly configured
export const isAdMobConfigured = (): boolean => {
  return !!(ADMOB_CONFIG.APP_ID.android);
};

// Helper function to get app ID for current platform
export const getAppId = (): string => {
  return ADMOB_CONFIG.APP_ID[Platform.OS] || ADMOB_CONFIG.APP_ID.android;
};
