import { Platform } from 'react-native';
import { 
  setRequestConfiguration,
  RequestConfiguration,
  MaxAdContentRating,
  TagForChildDirectedTreatment,
  TagForUnderAgeOfConsent,
} from 'react-native-google-mobile-ads';

// AdMob Configuration
export const ADMOB_CONFIG = {
  // 🔥 REPLACE THESE WITH YOUR ACTUAL ADMOB IDS 🔥
  APP_ID: {
    android: 'ca-app-pub-YOUR_ANDROID_APP_ID~YOUR_APP_ID',
    ios: 'ca-app-pub-YOUR_IOS_APP_ID~YOUR_APP_ID',
  },
  
  // Banner Ad Unit IDs
  BANNER_ADS: {
    home: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_BANNER_AD_UNIT_ID_1',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_BANNER_AD_UNIT_ID_1',
    },
    explore: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_BANNER_AD_UNIT_ID_2',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_BANNER_AD_UNIT_ID_2',
    },
    female: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_BANNER_AD_UNIT_ID_3',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_BANNER_AD_UNIT_ID_3',
    },
    settings: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_BANNER_AD_UNIT_ID_4',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_BANNER_AD_UNIT_ID_4',
    },
  },
  
  // Interstitial Ad Unit IDs
  INTERSTITIAL_ADS: {
    screenTransition: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_INTERSTITIAL_AD_UNIT_ID_1',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_INTERSTITIAL_AD_UNIT_ID_1',
    },
    modelView: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_INTERSTITIAL_AD_UNIT_ID_2',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_INTERSTITIAL_AD_UNIT_ID_2',
    },
  },
  
  // Rewarded Ad Unit IDs
  REWARDED_ADS: {
    premiumFeatures: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_REWARDED_AD_UNIT_ID_1',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_REWARDED_AD_UNIT_ID_1',
    },
    extraColors: {
      android: 'ca-app-pub-YOUR_ANDROID_APP_ID/YOUR_REWARDED_AD_UNIT_ID_2',
      ios: 'ca-app-pub-YOUR_IOS_APP_ID/YOUR_REWARDED_AD_UNIT_ID_2',
    },
  },
  
  // Test Ad Unit IDs (for development)
  TEST_ADS: {
    banner: Platform.select({
      android: 'ca-app-pub-3940256099942544/6300978111',
      ios: 'ca-app-pub-3940256099942544/2934735716',
    }),
    interstitial: Platform.select({
      android: 'ca-app-pub-3940256099942544/1033173712',
      ios: 'ca-app-pub-3940256099942544/4411468910',
    }),
    rewarded: Platform.select({
      android: 'ca-app-pub-3940256099942544/5224354917',
      ios: 'ca-app-pub-3940256099942544/1712485313',
    }),
  },
};

// Helper function to get ad unit ID based on platform
export const getAdUnitId = (adConfig: { android: string; ios: string }) => {
  return Platform.select(adConfig) || '';
};

// Helper function to get test ad unit ID
export const getTestAdUnitId = (adType: 'banner' | 'interstitial' | 'rewarded') => {
  return ADMOB_CONFIG.TEST_ADS[adType] || '';
};

// AdMob initialization and configuration
export const initializeAdMob = async () => {
  try {
    // Configure AdMob request settings
    const requestConfiguration: RequestConfiguration = {
      // Set maximum ad content rating
      maxAdContentRating: MaxAdContentRating.PG,
      
      // Set tag for child-directed treatment
      tagForChildDirectedTreatment: TagForChildDirectedTreatment.FALSE,
      
      // Set tag for users under age of consent
      tagForUnderAgeOfConsent: TagForUnderAgeOfConsent.FALSE,
      
      // Add test device IDs for development
      testDeviceIdentifiers: [
        // Add your test device IDs here
        // 'YOUR_TEST_DEVICE_ID_1',
        // 'YOUR_TEST_DEVICE_ID_2',
      ],
    };

    await setRequestConfiguration(requestConfiguration);
    console.log('AdMob initialized successfully');
  } catch (error) {
    console.error('Failed to initialize AdMob:', error);
  }
};

// Development mode flag
export const isDevelopmentMode = __DEV__;

// Get appropriate ad unit ID based on development mode
export const getAdUnitIdForEnvironment = (
  productionConfig: { android: string; ios: string },
  testAdType: 'banner' | 'interstitial' | 'rewarded'
) => {
  if (isDevelopmentMode) {
    return getTestAdUnitId(testAdType);
  }
  return getAdUnitId(productionConfig);
};

// Ad frequency management
export const AD_FREQUENCY = {
  INTERSTITIAL_MIN_INTERVAL: 60000, // 1 minute between interstitial ads
  BANNER_REFRESH_INTERVAL: 30000,   // 30 seconds banner refresh
  REWARDED_COOLDOWN: 300000,        // 5 minutes cooldown for rewarded ads
};

// Ad placement tracking
let lastInterstitialTime = 0;
let lastRewardedTime = 0;

export const canShowInterstitial = (): boolean => {
  const now = Date.now();
  return now - lastInterstitialTime >= AD_FREQUENCY.INTERSTITIAL_MIN_INTERVAL;
};

export const markInterstitialShown = (): void => {
  lastInterstitialTime = Date.now();
};

export const canShowRewarded = (): boolean => {
  const now = Date.now();
  return now - lastRewardedTime >= AD_FREQUENCY.REWARDED_COOLDOWN;
};

export const markRewardedShown = (): void => {
  lastRewardedTime = Date.now();
};
