import { Platform } from 'react-native';
import mobileAds, {
  MaxAdContentRating,
} from 'react-native-google-mobile-ads';

// AdMob Configuration
export const ADMOB_CONFIG = {
  // Your actual AdMob App ID
  APP_ID: {
    android: 'ca-app-pub-4977543488988533~1311624736',
  },

  // Banner Ad Unit IDs (using your single banner ad unit for all locations)
  BANNER_ADS: {
    home: {
      android: 'ca-app-pub-4977543488988533/9925128841',
    },
    explore: {
      android: 'ca-app-pub-4977543488988533/9925128841',
    },
    female: {
      android: 'ca-app-pub-4977543488988533/9925128841',
    },
    settings: {
      android: 'ca-app-pub-4977543488988533/9925128841',
    },
  },

  // Interstitial Ad Unit IDs (using your fullpage interstitial)
  INTERSTITIAL_ADS: {
    screenTransition: {
      android: 'ca-app-pub-4977543488988533/4624183785',
    },
    modelView: {
      android: 'ca-app-pub-4977543488988533/4624183785',
    },
  },

  // Native Advanced Ad Unit IDs (using your native advanced ads)
  NATIVE_ADS: {
    matchWithApp1: {
      android: 'ca-app-pub-4977543488988533/9883129763',
    },
    matchWithApp2: {
      android: 'ca-app-pub-4977543488988533/8612047174',
    },
  },
  
  // Test Ad Unit IDs (for development)
  TEST_ADS: {
    banner: Platform.select({
      android: 'ca-app-pub-3940256099942544/6300978111',
      ios: 'ca-app-pub-3940256099942544/2934735716',
    }),
    interstitial: Platform.select({
      android: 'ca-app-pub-3940256099942544/1033173712',
      ios: 'ca-app-pub-3940256099942544/4411468910',
    }),
    rewarded: Platform.select({
      android: 'ca-app-pub-3940256099942544/5224354917',
      ios: 'ca-app-pub-3940256099942544/1712485313',
    }),
  },
};

// Helper function to get ad unit ID for Android
export const getAdUnitId = (adConfig: { android: string }) => {
  return adConfig.android || '';
};

// Helper function to get test ad unit ID
export const getTestAdUnitId = (adType: 'banner' | 'interstitial' | 'rewarded') => {
  return ADMOB_CONFIG.TEST_ADS[adType] || '';
};

// AdMob initialization and configuration
export const initializeAdMob = async () => {
  try {
    // Initialize AdMob
    await mobileAds().initialize();

    // Configure AdMob request settings
    await mobileAds().setRequestConfiguration({
      // Set maximum ad content rating
      maxAdContentRating: MaxAdContentRating.PG,

      // Set tag for child-directed treatment (false = not child-directed)
      tagForChildDirectedTreatment: false,

      // Set tag for users under age of consent (false = not under age)
      tagForUnderAgeOfConsent: false,

      // Add test device IDs for development
      testDeviceIdentifiers: [
        // Add your test device IDs here
        // 'YOUR_TEST_DEVICE_ID_1',
        // 'YOUR_TEST_DEVICE_ID_2',
      ],
    });

    console.log('AdMob initialized successfully');
  } catch (error) {
    console.error('Failed to initialize AdMob:', error);
  }
};

// Development mode flag
export const isDevelopmentMode = __DEV__;

// Get appropriate ad unit ID based on development mode
export const getAdUnitIdForEnvironment = (
  productionConfig: { android: string },
  testAdType: 'banner' | 'interstitial' | 'rewarded'
) => {
  if (isDevelopmentMode) {
    return getTestAdUnitId(testAdType);
  }
  return getAdUnitId(productionConfig);
};

// Ad frequency management
export const AD_FREQUENCY = {
  INTERSTITIAL_MIN_INTERVAL: 60000, // 1 minute between interstitial ads
  BANNER_REFRESH_INTERVAL: 30000,   // 30 seconds banner refresh
  REWARDED_COOLDOWN: 300000,        // 5 minutes cooldown for rewarded ads
};

// Ad placement tracking
let lastInterstitialTime = 0;
let lastRewardedTime = 0;

export const canShowInterstitial = (): boolean => {
  const now = Date.now();
  return now - lastInterstitialTime >= AD_FREQUENCY.INTERSTITIAL_MIN_INTERVAL;
};

export const markInterstitialShown = (): void => {
  lastInterstitialTime = Date.now();
};

export const canShowRewarded = (): boolean => {
  const now = Date.now();
  return now - lastRewardedTime >= AD_FREQUENCY.REWARDED_COOLDOWN;
};

export const markRewardedShown = (): void => {
  lastRewardedTime = Date.now();
};
