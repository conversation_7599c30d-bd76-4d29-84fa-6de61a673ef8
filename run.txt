npx expo start --tunnel
https://res.cloudinary.com/dnk1sml4q/image/upload/v1750566426/llmavpowyxnkuz9n2kbs.glb




The field "cli.appVersionSource" is not set, but it will be required in the future. Learn more: https://docs.expo.dev/build-reference/app-versions/
Resolved "preview" environment for the build. Learn more: https://docs.expo.dev/eas/environment-variables/#setting-the-environment-for-your-builds
No environment variables with visibility "Plain text" and "Sensitive" found for the "preview" environment on EAS.

The field "cli.appVersionSource" is not set, but it will be required in the future. Learn more: https://docs.expo.dev/build-reference/app-versions/
✔ Using remote Android credentials (Expo server)



Running "expo doctor"
Running 15 checks on your project...
14/15 checks passed. 1 checks failed. Possible issues detected:
Use the --verbose flag to see more details about passed checks.
✖ Validate packages against React Native Directory package metadata
The following issues were found when validating your dependencies against React Native Directory:
  No metadata available: @react-three/drei, @react-three/fiber, react-toastify, three, three-stdlib
Advice:
Update React Native Directory to include metadata for unknown packages. Alternatively, set expo.doctor.reactNativeDirectoryCheck.listUnknownPackages in package.json to false to skip warnings about packages with no metadata, if the warning is not relevant.
1 check failed, indicating possible issues with the project.
Command "expo doctor" failed.
npx -y expo-doctor exited with non-zero code: 1