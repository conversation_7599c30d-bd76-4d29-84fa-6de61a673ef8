// metro.config.js
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add support for GLB and GLTF files
config.resolver.assetExts.push('glb', 'gltf', 'bin', 'jpg', 'jpeg', 'png');

// Add .mjs extension support for @iabtcf/core and other ESM packages
config.resolver.sourceExts.push('mjs');

// Add path alias support
config.resolver.alias = {
  '@': path.resolve(__dirname, './'),
};

// Fix module resolution for @iabtcf/core and similar packages
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Custom resolver to handle @iabtcf/core module resolution issues
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // Handle @iabtcf/core field/index.js resolution issue
  if (moduleName.includes('@iabtcf/core') && moduleName.includes('field/index.js')) {
    try {
      // Try to resolve the field directory index
      const fieldPath = moduleName.replace('field/index.js', 'field/index.mjs');
      return context.resolveRequest(context, fieldPath, platform);
    } catch (e) {
      // Fallback to default resolution
      return context.resolveRequest(context, moduleName, platform);
    }
  }

  // Default resolution for other modules
  return context.resolveRequest(context, moduleName, platform);
};

// Optional: Add transformer for better asset handling
config.transformer = {
  ...config.transformer,
  assetPlugins: ['expo-asset/tools/hashAssetFiles'],
  // Enable .mjs transformation
  babelTransformerPath: require.resolve('metro-react-native-babel-transformer'),
};

// Increase the maximum bundle size if needed
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Add unstable_enablePackageExports for better ESM support
config.resolver.unstable_enablePackageExports = true;

module.exports = config;